import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AuthProvider } from './contexts/AuthContext';
import { ErrorBoundary } from './components/ui/error-boundary';
import { Header } from './components/layout/Header';
import { SearchBar } from './components/search/SearchBar';
import { PhotoGallery } from './components/gallery/PhotoGallery';
import { PhotoModal } from './components/gallery/PhotoModal';
import { PhotoGridSkeleton } from './components/ui/loading-spinner';
import { pexelsApi } from './services/pexelsApi';
import type { PexelsPhoto, SearchState } from './types';
import { debounce } from './lib/utils';

function AppContent() {
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    results: [],
    isLoading: false,
    error: null,
    page: 1,
    hasMore: true,
    searchHistory: [],
  });

  const [selectedPhoto, setSelectedPhoto] = useState<PexelsPhoto | null>(null);
  const [isPhotoModalOpen, setIsPhotoModalOpen] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Load curated photos on initial load
  useEffect(() => {
    loadCuratedPhotos();
  }, []);

  const loadCuratedPhotos = async () => {
    setSearchState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await pexelsApi.getCuratedPhotos(1, 15);

      if (response.success && response.data) {
        setSearchState(prev => ({
          ...prev,
          results: response.data!.photos,
          isLoading: false,
          page: 1,
          hasMore: response.data!.photos.length === 15,
          query: '',
        }));
      } else {
        setSearchState(prev => ({
          ...prev,
          error: response.error || 'Failed to load photos',
          isLoading: false,
        }));
      }
    } catch (error) {
      setSearchState(prev => ({
        ...prev,
        error: 'Failed to load photos',
        isLoading: false,
      }));
    } finally {
      setIsInitialLoad(false);
    }
  };

  const handleSearch = useCallback(async (query: string) => {
    if (!query.trim()) return;

    setSearchState(prev => ({
      ...prev,
      isLoading: true,
      error: null,
      query: query.trim(),
      page: 1,
    }));

    try {
      const response = await pexelsApi.searchPhotos({
        query: query.trim(),
        page: 1,
        perPage: 15,
      });

      if (response.success && response.data) {
        setSearchState(prev => ({
          ...prev,
          results: response.data!.photos,
          isLoading: false,
          hasMore: response.data!.photos.length === 15,
        }));
      } else {
        setSearchState(prev => ({
          ...prev,
          error: response.error || 'Search failed',
          isLoading: false,
          results: [],
        }));
      }
    } catch (error) {
      setSearchState(prev => ({
        ...prev,
        error: 'Search failed',
        isLoading: false,
        results: [],
      }));
    }
  }, []);

  const handleLoadMore = useCallback(async () => {
    if (searchState.isLoading || !searchState.hasMore) return;

    const nextPage = searchState.page + 1;
    setSearchState(prev => ({ ...prev, isLoading: true }));

    try {
      const response = searchState.query
        ? await pexelsApi.searchPhotos({
            query: searchState.query,
            page: nextPage,
            perPage: 15,
          })
        : await pexelsApi.getCuratedPhotos(nextPage, 15);

      if (response.success && response.data) {
        setSearchState(prev => ({
          ...prev,
          results: [...prev.results, ...response.data!.photos],
          isLoading: false,
          page: nextPage,
          hasMore: response.data!.photos.length === 15,
        }));
      } else {
        setSearchState(prev => ({
          ...prev,
          error: response.error || 'Failed to load more photos',
          isLoading: false,
        }));
      }
    } catch (error) {
      setSearchState(prev => ({
        ...prev,
        error: 'Failed to load more photos',
        isLoading: false,
      }));
    }
  }, [searchState.query, searchState.page, searchState.isLoading, searchState.hasMore]);

  const handlePhotoClick = (photo: PexelsPhoto) => {
    setSelectedPhoto(photo);
    setIsPhotoModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsPhotoModalOpen(false);
    setSelectedPhoto(null);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Search Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <SearchBar
            onSearch={handleSearch}
            isLoading={searchState.isLoading}
          />
        </motion.div>

        {/* Gallery Section */}
        <AnimatePresence mode="wait">
          {isInitialLoad ? (
            <motion.div
              key="skeleton"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <PhotoGridSkeleton />
            </motion.div>
          ) : (
            <motion.div
              key="gallery"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <PhotoGallery
                photos={searchState.results}
                isLoading={searchState.isLoading}
                error={searchState.error}
                hasMore={searchState.hasMore}
                onLoadMore={handleLoadMore}
                onPhotoClick={handlePhotoClick}
                searchQuery={searchState.query}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </main>

      {/* Photo Modal */}
      <PhotoModal
        photo={selectedPhoto}
        isOpen={isPhotoModalOpen}
        onClose={handleCloseModal}
      />

      {/* Footer */}
      <footer className="border-t bg-muted/50 py-6 mt-16">
        <div className="container mx-auto px-4 text-center">
          <p className="text-sm text-muted-foreground">
            Powered by{' '}
            <a
              href="https://www.pexels.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              Pexels
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
