import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Download,
  ExternalLink,
  User,
  Copy,
  Share2,
  Eye,
  Camera,
  Maximize2
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogOverlay,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import { Skeleton } from '../ui/skeleton';
import type { PhotoModalProps } from '../../types';
import { downloadImage, copyToClipboard } from '../../lib/utils';

export function PhotoModal({ photo, isOpen, onClose }: PhotoModalProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [selectedSize, setSelectedSize] = useState<'original' | 'large' | 'medium' | 'small'>('original');
  const [isInfoLoading, setIsInfoLoading] = useState(true);

  useEffect(() => {
    if (isOpen && photo) {
      setIsImageLoaded(false);
      setSelectedSize('original');
      setIsInfoLoading(true);

      // Simulate loading time for info panel
      const timer = setTimeout(() => {
        setIsInfoLoading(false);
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [isOpen, photo]);

  if (!photo) return null;

  const handleDownload = (size: keyof typeof photo.src = 'original') => {
    const url = photo.src[size];
    downloadImage(url, `photo-${photo.id}-${size}.jpg`);
  };

  const handleCopyLink = async () => {
    try {
      await copyToClipboard(photo.url);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Photo by ${photo.photographer}`,
          text: photo.alt || `Beautiful photo by ${photo.photographer}`,
          url: photo.url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      handleCopyLink();
    }
  };

  const sizeOptions = [
    { key: 'small' as const, label: 'Small', dimensions: '640×427' },
    { key: 'medium' as const, label: 'Medium', dimensions: '1280×853' },
    { key: 'large' as const, label: 'Large', dimensions: '1920×1280' },
    { key: 'original' as const, label: 'Original', dimensions: `${photo.width}×${photo.height}` },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="bg-background/80 backdrop-blur-sm" />
      <DialogContent className="max-w-[95vw] w-full h-[95vh] p-0 border-0 bg-transparent overflow-hidden text-white">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative w-full h-full flex flex-col lg:flex-row bg-background text-foreground rounded-lg overflow-hidden shadow-2xl"
        >
          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="absolute top-4 right-4 z-50 bg-background/80 hover:bg-background text-foreground border h-10 w-10 backdrop-blur-sm"
          >
            <X className="h-5 w-5" />
          </Button>

          {/* Image container */}
          <div className="flex-1 relative flex items-center justify-center p-4 lg:p-6 bg-black/5">
            <motion.div
              layoutId={`photo-${photo.id}`}
              className="relative w-full h-full max-w-none max-h-none flex items-center justify-center cursor-pointer group"
            >
              {!isImageLoaded && (
                <div className="absolute inset-0 bg-muted animate-pulse rounded-lg flex items-center justify-center">
                  <div className="text-center space-y-2">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                      className="w-8 h-8 border-2 border-muted-foreground/30 border-t-muted-foreground rounded-full mx-auto"
                    />
                    <div className="text-muted-foreground">Loading high resolution image...</div>
                  </div>
                </div>
              )}

              <img
                src={photo.src[selectedSize]}
                alt={photo.alt || `Photo by ${photo.photographer}`}
                className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-all duration-300 ${
                  isImageLoaded ? 'opacity-100 group-hover:scale-105' : 'opacity-0'
                }`}
                onLoad={() => setIsImageLoaded(true)}
                style={{
                  objectFit: 'contain',
                  width: 'auto',
                  height: 'auto',
                  maxWidth: '100%',
                  maxHeight: '100%'
                }}
              />

              {/* Zoom overlay */}
              {isImageLoaded && (
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 rounded-lg flex items-center justify-center">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileHover={{ opacity: 1, scale: 1 }}
                    className="bg-black/50 backdrop-blur-sm rounded-full p-3 text-white"
                  >
                    <Maximize2 className="h-6 w-6" />
                  </motion.div>
                </div>
              )}

              {/* Mobile info section */}
              <div className="lg:hidden mt-6 space-y-4 bg-background">
                {/* Photographer info */}
                <Card className="bg-card border border-border">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="h-12 w-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-card-foreground">{photo.photographer}</h3>
                        <p className="text-sm text-muted-foreground">Professional Photographer</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(photo.photographer_url, '_blank')}
                        className="border-border text-foreground hover:bg-accent"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                    {photo.alt && (
                      <p className="text-sm text-muted-foreground">{photo.alt}</p>
                    )}
                  </CardContent>
                </Card>

                {/* Quick actions */}
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => handleDownload('original')}
                    className="flex items-center justify-center space-x-2 bg-primary text-primary-foreground hover:bg-primary/90"
                  >
                    <Download className="h-4 w-4" />
                    <span>Download</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleShare}
                    className="flex items-center justify-center space-x-2 border-border text-foreground hover:bg-accent"
                  >
                    <Share2 className="h-4 w-4" />
                    <span>Share</span>
                  </Button>
                </div>

                {/* Photo details */}
                <Card className="bg-card border border-border">
                  <CardContent className="p-4">
                    <h4 className="font-semibold mb-3 text-card-foreground">Photo Details</h4>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-muted-foreground">Dimensions</span>
                        <p className="font-medium text-card-foreground">{photo.width} × {photo.height}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Photo ID</span>
                        <p className="font-medium text-card-foreground">#{photo.id}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Color</span>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded border border-border"
                            style={{ backgroundColor: photo.avg_color }}
                          />
                          <span className="font-medium text-xs text-card-foreground">{photo.avg_color}</span>
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Orientation</span>
                        <p className="font-medium capitalize text-card-foreground">
                          {photo.width > photo.height ? 'Landscape' : photo.height > photo.width ? 'Portrait' : 'Square'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          </div>

          {/* Info panel */}
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="hidden lg:flex lg:w-96 bg-background text-foreground border-l border-border flex-col"
          >
            <div className="h-full overflow-y-auto elegant-scroll p-6 space-y-6">
              {/* Loading state for info panel */}
              <AnimatePresence>
                {isInfoLoading && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10"
                  >
                    <div className="text-center space-y-4">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                        className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full mx-auto"
                      />
                      <div className="text-muted-foreground">
                        <p className="font-medium">Loading photo details...</p>
                        <p className="text-sm">Fetching photographer information</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
              {/* Photographer info */}
              <Card className="bg-card border border-border">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="h-16 w-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                        <User className="h-8 w-8 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold truncate text-card-foreground">{photo.photographer}</h3>
                        <p className="text-sm text-muted-foreground">Professional Photographer</p>
                        <p className="text-xs text-muted-foreground">ID: {photo.photographer_id}</p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(photo.photographer_url, '_blank')}
                        className="flex-1 border-border text-foreground hover:bg-accent"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Profile
                      </Button>
                    </div>

                    {/* Photo description */}
                    {photo.alt && (
                      <div className="pt-4 border-t border-border">
                        <h4 className="text-sm font-medium mb-2 text-card-foreground">Description</h4>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {photo.alt}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Photo details */}
              <Card className="bg-card border border-border">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2 text-card-foreground">
                    <Camera className="h-4 w-4 text-primary" />
                    Photo Details
                  </h4>

                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Dimensions</span>
                      <span className="text-sm font-medium text-card-foreground">{photo.width} × {photo.height}</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Photo ID</span>
                      <span className="text-sm font-medium text-card-foreground">#{photo.id}</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Average Color</span>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-6 h-6 rounded-md border-2 border-border shadow-sm"
                          style={{ backgroundColor: photo.avg_color }}
                        />
                        <span className="text-sm font-medium font-mono text-card-foreground">{photo.avg_color}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Aspect Ratio</span>
                      <span className="text-sm font-medium text-card-foreground">
                        {(photo.width / photo.height).toFixed(2)}:1
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Orientation</span>
                      <span className="text-sm font-medium capitalize text-card-foreground">
                        {photo.width > photo.height ? 'Landscape' : photo.height > photo.width ? 'Portrait' : 'Square'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Size selector */}
              <Card className="bg-card border border-border">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2 text-card-foreground">
                    <Eye className="h-4 w-4 text-primary" />
                    View Size
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    {sizeOptions.map((option) => (
                      <Button
                        key={option.key}
                        variant={selectedSize === option.key ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedSize(option.key)}
                        className={`flex flex-col items-center p-4 h-auto transition-all ${
                          selectedSize === option.key
                            ? "bg-primary text-primary-foreground"
                            : "border-border text-foreground hover:bg-accent"
                        }`}
                      >
                        <span className="font-medium">{option.label}</span>
                        <span className="text-xs opacity-70">{option.dimensions}</span>
                      </Button>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Currently viewing: <span className="font-medium text-card-foreground">{selectedSize}</span> size
                  </p>
                </CardContent>
              </Card>

              {/* Download options */}
              <Card className="bg-card border border-border">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2 text-card-foreground">
                    <Download className="h-4 w-4 text-primary" />
                    Download Options
                  </h4>
                  <div className="space-y-3">
                    {sizeOptions.map((option) => (
                      <Button
                        key={`download-${option.key}`}
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownload(option.key)}
                        className="w-full justify-between p-4 h-auto hover:bg-primary/5 border-border text-foreground"
                      >
                        <div className="text-left">
                          <div className="font-medium text-card-foreground">{option.label}</div>
                          <div className="text-xs text-muted-foreground">{option.dimensions}</div>
                        </div>
                        <Download className="h-4 w-4" />
                      </Button>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Free to use under Pexels License
                  </p>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card className="bg-card border border-border">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold text-card-foreground">Share & Actions</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleShare}
                      className="flex items-center justify-center space-x-2 p-3 border-border text-foreground hover:bg-accent"
                    >
                      <Share2 className="h-4 w-4" />
                      <span>Share</span>
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLink}
                      className="flex items-center justify-center space-x-2 p-3 border-border text-foreground hover:bg-accent"
                    >
                      <Copy className="h-4 w-4" />
                      <span>Copy Link</span>
                    </Button>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(photo.url, '_blank')}
                    className="w-full flex items-center justify-center space-x-2 p-3 border-border text-foreground hover:bg-accent"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span>View on Pexels</span>
                  </Button>
                </CardContent>
              </Card>

              {/* Attribution */}
              <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold text-card-foreground">Attribution</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      Photo by{' '}
                      <a
                        href={photo.photographer_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline font-medium"
                      >
                        {photo.photographer}
                      </a>
                      {' '}on{' '}
                      <a
                        href="https://www.pexels.com"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline font-medium"
                      >
                        Pexels
                      </a>
                    </p>
                    <p className="text-xs text-muted-foreground">
                      This photo is free to use under the Pexels License. No attribution required, but appreciated.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </motion.div>


      </DialogContent>
    </Dialog>
  );
}
