import { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  X,
  Download,
  ExternalLink,
  Copy,
  Share2,
  Eye,
  Camera,
  Maximize2,
  Heart
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
// import { Skeleton } from '../ui/skeleton';
import { ImageViewer } from './ImageViewer';
import type { PhotoModalProps } from '../../types';
import { downloadImage, copyToClipboard } from '../../lib/utils';
import { createPhotographerProfile } from '../../lib/avatarUtils';
import { useLikes } from '../../services/likeService';
import { generateColorPalette, generateCSSVariables } from '../../lib/colorUtils';

export function PhotoModal({ photo, isOpen, onClose }: PhotoModalProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [selectedSize, setSelectedSize] = useState<'original' | 'large' | 'medium' | 'small'>('original');
  const [isInfoLoading, setIsInfoLoading] = useState(true);
  const [isImageViewerOpen, setIsImageViewerOpen] = useState(false);

  const { isLiked, toggleLike } = useLikes();
  const photographerProfile = photo ? createPhotographerProfile(
    photo.photographer_id,
    photo.photographer,
    photo.photographer_url
  ) : null;

  // Generate dynamic color palette based on image's average color
  const colorPalette = useMemo(() => {
    if (!photo?.avg_color) return null;
    return generateColorPalette(photo.avg_color);
  }, [photo?.avg_color]);

  const cssVariables = useMemo(() => {
    if (!colorPalette) return {};
    return generateCSSVariables(colorPalette);
  }, [colorPalette]);

  useEffect(() => {
    if (isOpen && photo) {
      setIsImageLoaded(false);
      setSelectedSize('original');
      setIsInfoLoading(true);
      setIsImageViewerOpen(false);

      // Simulate loading time for info panel
      const timer = setTimeout(() => {
        setIsInfoLoading(false);
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [isOpen, photo]);

  if (!photo) return null;

  const handleDownload = (size: keyof typeof photo.src = 'original') => {
    const url = photo.src[size];
    downloadImage(url, `photo-${photo.id}-${size}.jpg`);
  };

  const handleCopyLink = async () => {
    try {
      await copyToClipboard(photo.url);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Photo by ${photo.photographer}`,
          text: photo.alt || `Beautiful photo by ${photo.photographer}`,
          url: photo.url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      handleCopyLink();
    }
  };

  // Early return if no photo
  if (!photo) {
    return null;
  }



  const sizeOptions = [
    { key: 'small' as const, label: 'Small', dimensions: '640×427' },
    { key: 'medium' as const, label: 'Medium', dimensions: '1280×853' },
    { key: 'large' as const, label: 'Large', dimensions: '1920×1280' },
    { key: 'original' as const, label: 'Original', dimensions: `${photo.width}×${photo.height}` },
  ];

  return (
    <TooltipProvider>
      <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="photo-modal-mobile photo-modal-desktop [&>button]:hidden"
        style={{
          backgroundColor: colorPalette ? `${colorPalette.background}f0` : 'hsl(var(--background) / 0.95)',
          borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))',
          color: colorPalette ? colorPalette.foreground : 'hsl(var(--foreground))',
          backdropFilter: 'blur(12px)',
          display: 'flex',
          flexDirection: 'column',
          position: 'fixed',
          padding: 0,
          margin: 0,
          border: '1px solid',
          borderRadius: '0.5rem',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          // Default desktop centering
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '90vw',
          height: '90vh',
          maxWidth: '90vw',
          maxHeight: '90vh',
          ...cssVariables
        }}
      >

        {/* Header with dynamic background */}
        <div
          className="absolute top-0 left-0 right-0 h-20 z-40 pointer-events-none"
          style={{
            background: colorPalette
              ? `linear-gradient(180deg, ${colorPalette.background}e6 0%, ${colorPalette.background}80 50%, transparent 100%)`
              : 'linear-gradient(180deg, hsl(var(--background) / 0.9) 0%, hsl(var(--background) / 0.5) 50%, transparent 100%)'
          }}
        />
        <div
          className="relative w-full flex-1 flex flex-col md:flex-row photo-modal-mobile-content sm:rounded-lg overflow-hidden"
          style={{
            backgroundColor: colorPalette ? colorPalette.background : 'hsl(var(--background))',
            color: colorPalette ? colorPalette.foreground : 'hsl(var(--foreground))',
            minHeight: '0',
            height: '100%'
          }}
        >

          {/* Close button */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="absolute top-2 right-2 sm:top-4 sm:right-4 z-50 text-foreground border h-12 w-12 sm:h-12 sm:w-12 backdrop-blur-sm shadow-lg touch-manipulation pointer-events-auto"
                style={{
                  backgroundColor: colorPalette ? `${colorPalette.background}cc` : 'hsl(var(--background) / 0.8)',
                  borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))',
                  color: colorPalette ? colorPalette.foreground : 'hsl(var(--foreground))'
                }}
              >
                <X className="h-6 w-6 sm:h-6 sm:w-6" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Close photo viewer (Esc)</p>
            </TooltipContent>
          </Tooltip>

          {/* Image container - Mobile: full width, Desktop: flex-1 */}
          <div
            className="relative flex items-center justify-center w-full photo-modal-image-mobile h-[50vh] sm:h-[55vh] md:flex-1 md:h-auto md:min-h-0 md:p-6"
            style={{
              backgroundColor: colorPalette ? `${colorPalette.muted}40` : 'hsl(var(--muted) / 0.3)'
            }}
          >
            <motion.div
              layoutId={`photo-${photo.id}`}
              className="relative w-full h-full max-w-none max-h-none flex items-center justify-center cursor-pointer group"
              onClick={() => setIsImageViewerOpen(true)}
            >
              {!isImageLoaded && (
                <div className="absolute inset-0 bg-muted animate-pulse rounded-lg flex items-center justify-center">
                  <div className="text-center space-y-2">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                      className="w-8 h-8 border-2 border-muted-foreground/30 border-t-muted-foreground rounded-full mx-auto"
                    />
                    <div className="text-muted-foreground">Loading high resolution image...</div>
                  </div>
                </div>
              )}

              <img
                src={photo.src[selectedSize]}
                alt={photo.alt || `Photo by ${photo.photographer}`}
                className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-all duration-300 ${isImageLoaded ? 'opacity-100 group-hover:scale-105' : 'opacity-0'
                  }`}
                onLoad={() => setIsImageLoaded(true)}
                style={{
                  objectFit: 'contain',
                  width: 'auto',
                  height: 'auto',
                  maxWidth: '100%',
                  maxHeight: '100%'
                }}
              />

              {/* Zoom overlay */}
              {isImageLoaded && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className="absolute inset-0 transition-all duration-300 rounded-lg flex items-center justify-center cursor-pointer"
                      style={{
                        backgroundColor: 'transparent'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = colorPalette ? `${colorPalette.background}33` : 'hsl(var(--background) / 0.2)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        whileHover={{ opacity: 1, scale: 1 }}
                        className="rounded-full p-3 shadow-lg backdrop-blur-sm"
                        style={{
                          backgroundColor: colorPalette ? `${colorPalette.background}e6` : 'hsl(var(--background) / 0.9)',
                          color: colorPalette ? colorPalette.foreground : 'hsl(var(--foreground))',
                          borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))',
                          border: '1px solid'
                        }}
                      >
                        <Maximize2 className="h-6 w-6" />
                      </motion.div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Click to view full-size image with zoom controls</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </motion.div>
          </div>

          {/* Mobile info section - Column layout below image */}
          <div
            className="md:hidden photo-modal-info-mobile flex-1 min-h-0 overflow-hidden"
            style={{
              backgroundColor: colorPalette ? colorPalette.background : 'hsl(var(--background))'
            }}
          >
            <div className="h-full overflow-y-auto elegant-scroll p-4 space-y-4">
                {/* Creator Profile Card */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Card
                      className="border cursor-help"
                      style={{
                        backgroundColor: colorPalette ? `${colorPalette.background}cc` : 'hsl(var(--card))',
                        borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))'
                      }}
                    >
                  <CardContent className="p-4">
                    {/* Creator Header */}
                    <div className="flex items-center space-x-3 mb-4">
                      <img
                        src={photographerProfile?.avatar}
                        alt={photo.photographer}
                        className="h-12 w-12 rounded-full bg-muted flex-shrink-0 ring-2 ring-primary/20"
                      />
                      <div className="flex-1 min-w-0">
                        <h3
                          className="font-bold text-lg truncate"
                          style={{ color: colorPalette ? colorPalette.foreground : 'hsl(var(--card-foreground))' }}
                        >
                          {photo.photographer}
                        </h3>
                        <p
                          className="text-sm line-clamp-1"
                          style={{ color: colorPalette ? colorPalette.secondary : 'hsl(var(--muted-foreground))' }}
                        >
                          {photographerProfile?.bio}
                        </p>
                        <p
                          className="text-xs"
                          style={{ color: colorPalette ? colorPalette.secondary : 'hsl(var(--muted-foreground))' }}
                        >
                          {photographerProfile?.totalPhotos} photos • ID: {photo.photographer_id}
                        </p>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            onClick={() => toggleLike(photo.id)}
                            variant="outline"
                            className={`flex items-center space-x-2 ${
                              isLiked(photo.id)
                                ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                                : 'border-border text-foreground hover:bg-accent'
                            }`}
                          >
                            <Heart className={`h-4 w-4 ${isLiked(photo.id) ? 'fill-current' : ''}`} />
                            <span>Like</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{isLiked(photo.id) ? 'Remove from favorites' : 'Add to favorites'}</p>
                        </TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            onClick={() => window.open(photo.photographer_url, '_blank')}
                            className="flex items-center space-x-2 border-border text-foreground hover:bg-accent flex-1"
                          >
                            <ExternalLink className="h-4 w-4" />
                            <span>View Profile</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Visit {photo.photographer}'s profile on Pexels</p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </CardContent>
                </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Photographer information and actions</p>
                </TooltipContent>
              </Tooltip>

                {/* Description Card */}
                {photo.alt && (
                  <Card
                    className="border"
                    style={{
                      backgroundColor: colorPalette ? `${colorPalette.background}cc` : 'hsl(var(--card))',
                      borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))'
                    }}
                  >
                    <CardContent className="p-4">
                      <h4
                        className="font-semibold mb-2"
                        style={{ color: colorPalette ? colorPalette.foreground : 'hsl(var(--card-foreground))' }}
                      >
                        Description
                      </h4>
                      <p
                        className="text-sm leading-relaxed"
                        style={{ color: colorPalette ? colorPalette.secondary : 'hsl(var(--muted-foreground))' }}
                      >
                        {photo.alt}
                      </p>
                    </CardContent>
                  </Card>
                )}

                {/* Photo Details Card */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Card
                      className="border cursor-help"
                      style={{
                        backgroundColor: colorPalette ? `${colorPalette.background}cc` : 'hsl(var(--card))',
                        borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))'
                      }}
                    >
                  <CardContent className="p-4">
                    <h4
                      className="font-semibold mb-3 flex items-center space-x-2"
                      style={{ color: colorPalette ? colorPalette.foreground : 'hsl(var(--card-foreground))' }}
                    >
                      <Camera
                        className="h-4 w-4"
                        style={{ color: colorPalette ? colorPalette.primary : 'hsl(var(--primary))' }}
                      />
                      <span>Photo Details</span>
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span
                          className="text-sm"
                          style={{ color: colorPalette ? colorPalette.secondary : 'hsl(var(--muted-foreground))' }}
                        >
                          Dimensions
                        </span>
                        <span
                          className="font-medium"
                          style={{ color: colorPalette ? colorPalette.foreground : 'hsl(var(--card-foreground))' }}
                        >
                          {photo.width} × {photo.height}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span
                          className="text-sm"
                          style={{ color: colorPalette ? colorPalette.secondary : 'hsl(var(--muted-foreground))' }}
                        >
                          Photo ID
                        </span>
                        <span
                          className="font-medium"
                          style={{ color: colorPalette ? colorPalette.foreground : 'hsl(var(--card-foreground))' }}
                        >
                          #{photo.id}
                        </span>
                      </div>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex justify-between items-center cursor-help">
                            <span
                              className="text-sm"
                              style={{ color: colorPalette ? colorPalette.secondary : 'hsl(var(--muted-foreground))' }}
                            >
                              Average Color
                            </span>
                            <div className="flex items-center space-x-2">
                              <div
                                className="w-4 h-4 rounded border flex-shrink-0"
                                style={{
                                  backgroundColor: photo.avg_color,
                                  borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))'
                                }}
                              />
                              <span
                                className="font-medium text-xs font-mono"
                                style={{ color: colorPalette ? colorPalette.foreground : 'hsl(var(--card-foreground))' }}
                              >
                                {photo.avg_color}
                              </span>
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>The dominant color extracted from this image</p>
                        </TooltipContent>
                      </Tooltip>
                      <div className="flex justify-between items-center">
                        <span
                          className="text-sm"
                          style={{ color: colorPalette ? colorPalette.secondary : 'hsl(var(--muted-foreground))' }}
                        >
                          Orientation
                        </span>
                        <span
                          className="font-medium capitalize"
                          style={{ color: colorPalette ? colorPalette.foreground : 'hsl(var(--card-foreground))' }}
                        >
                          {photo.width > photo.height ? 'Landscape' : photo.height > photo.width ? 'Portrait' : 'Square'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Technical information about this photo</p>
                </TooltipContent>
              </Tooltip>

                {/* Action Buttons */}
                <div className="grid grid-cols-2 gap-3 pt-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={() => handleDownload('original')}
                        className="flex items-center justify-center space-x-2 bg-primary text-primary-foreground hover:bg-primary/90"
                      >
                        <Download className="h-4 w-4" />
                        <span>Download</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Download original image ({photo.width}×{photo.height})</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        onClick={handleShare}
                        className="flex items-center justify-center space-x-2 border-border text-foreground hover:bg-accent"
                      >
                        <Share2 className="h-4 w-4" />
                        <span>Share</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Share this photo with others</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
            </div>
          </div>

          {/* Info panel - Desktop/Tablet right sidebar */}
          <div
            className="hidden md:flex md:w-80 lg:w-96 border-l flex-col min-h-0"
            style={{
              backgroundColor: colorPalette ? colorPalette.background : 'hsl(var(--background))',
              color: colorPalette ? colorPalette.foreground : 'hsl(var(--foreground))',
              borderColor: colorPalette ? colorPalette.border : 'hsl(var(--border))'
            }}
          >

            <div className="h-full overflow-y-auto elegant-scroll p-6 space-y-6 relative">
              {/* Loading state for info panel */}
              <AnimatePresence>
                {isInfoLoading && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 bg-background/95 backdrop-blur-md flex items-center justify-center z-10 rounded-lg border border-border"
                  >
                    <div className="text-center space-y-4">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                        className="w-8 h-8 border-2 border-primary/30 border-t-primary rounded-full mx-auto"
                      />
                      <div className="text-muted-foreground">
                        <p className="font-medium">Loading photo details...</p>
                        <p className="text-sm">Fetching photographer information</p>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
              {/* Photographer info */}
              <Card className="bg-card border border-border">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <img
                            src={photographerProfile?.avatar}
                            alt={photo.photographer}
                            className="h-16 w-16 rounded-full bg-muted cursor-pointer hover:ring-2 hover:ring-primary/50 transition-all"
                          />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Click to view {photo.photographer}'s profile</p>
                        </TooltipContent>
                      </Tooltip>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold truncate text-card-foreground">{photo.photographer}</h3>
                        <p className="text-sm text-muted-foreground">{photographerProfile?.bio}</p>
                        <p className="text-xs text-muted-foreground">{photographerProfile?.totalPhotos} photos • ID: {photo.photographer_id}</p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        onClick={() => toggleLike(photo.id)}
                        size="sm"
                        className={`${
                          isLiked(photo.id)
                            ? 'bg-red-500 text-white hover:bg-red-600'
                            : 'bg-primary text-primary-foreground hover:bg-primary/90'
                        }`}
                      >
                        <Heart className={`h-4 w-4 mr-2 ${isLiked(photo.id) ? 'fill-current' : ''}`} />
                        {isLiked(photo.id) ? 'Liked' : 'Like'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(photo.photographer_url, '_blank')}
                        className="flex-1 border-border text-foreground hover:bg-accent"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Profile
                      </Button>
                    </div>

                    {/* Photo description */}
                    {photo.alt && (
                      <div className="pt-4 border-t border-border">
                        <h4 className="text-sm font-medium mb-2 text-card-foreground">Description</h4>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {photo.alt}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Photo details */}
              <Card className="bg-card border border-border">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2 text-card-foreground">
                    <Camera className="h-4 w-4 text-primary" />
                    Photo Details
                  </h4>

                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Dimensions</span>
                      <span className="text-sm font-medium text-card-foreground">{photo.width} × {photo.height}</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Photo ID</span>
                      <span className="text-sm font-medium text-card-foreground">#{photo.id}</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Average Color</span>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-6 h-6 rounded-md border-2 border-border shadow-sm"
                          style={{ backgroundColor: photo.avg_color }}
                        />
                        <span className="text-sm font-medium font-mono text-card-foreground">{photo.avg_color}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Aspect Ratio</span>
                      <span className="text-sm font-medium text-card-foreground">
                        {(photo.width / photo.height).toFixed(2)}:1
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Orientation</span>
                      <span className="text-sm font-medium capitalize text-card-foreground">
                        {photo.width > photo.height ? 'Landscape' : photo.height > photo.width ? 'Portrait' : 'Square'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Size selector */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="bg-card border border-border cursor-help">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2 text-card-foreground">
                    <Eye className="h-4 w-4 text-primary" />
                    View Size
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    {sizeOptions.map((option) => (
                      <Tooltip key={option.key}>
                        <TooltipTrigger asChild>
                          <Button
                            variant={selectedSize === option.key ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSelectedSize(option.key)}
                            className={`flex flex-col items-center p-4 h-auto transition-all ${selectedSize === option.key
                                ? "bg-primary text-primary-foreground"
                                : "border-border text-foreground hover:bg-accent"
                              }`}
                          >
                            <span className="font-medium">{option.label}</span>
                            <span className="text-xs opacity-70">{option.dimensions}</span>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Switch to {option.label.toLowerCase()} size ({option.dimensions})</p>
                        </TooltipContent>
                      </Tooltip>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Currently viewing: <span className="font-medium text-card-foreground">{selectedSize}</span> size
                  </p>
                </CardContent>
              </Card>
              </TooltipTrigger>
              <TooltipContent>
                <p>Change the viewing size of the image</p>
              </TooltipContent>
            </Tooltip>

              {/* Download options */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="bg-card border border-border cursor-help">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2 text-card-foreground">
                    <Download className="h-4 w-4 text-primary" />
                    Download Options
                  </h4>
                  <div className="space-y-3">
                    {sizeOptions.map((option) => (
                      <Tooltip key={`download-${option.key}`}>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownload(option.key)}
                            className="w-full justify-between p-4 h-auto hover:bg-primary/5 border-border text-foreground"
                          >
                            <div className="text-left">
                              <div className="font-medium text-card-foreground">{option.label}</div>
                              <div className="text-xs text-muted-foreground">{option.dimensions}</div>
                            </div>
                            <Download className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Download {option.label.toLowerCase()} size image ({option.dimensions})</p>
                        </TooltipContent>
                      </Tooltip>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Free to use under Pexels License
                  </p>
                </CardContent>
              </Card>
              </TooltipTrigger>
              <TooltipContent>
                <p>Download this image in different sizes</p>
              </TooltipContent>
            </Tooltip>

              {/* Actions */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="bg-card border border-border cursor-help">
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold text-card-foreground">Share & Actions</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleShare}
                          className="flex items-center justify-center space-x-2 p-3 border-border text-foreground hover:bg-accent"
                        >
                          <Share2 className="h-4 w-4" />
                          <span>Share</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Share this photo via native sharing or copy link</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCopyLink}
                          className="flex items-center justify-center space-x-2 p-3 border-border text-foreground hover:bg-accent"
                        >
                          <Copy className="h-4 w-4" />
                          <span>Copy Link</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Copy photo URL to clipboard</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(photo.url, '_blank')}
                        className="w-full flex items-center justify-center space-x-2 p-3 border-border text-foreground hover:bg-accent"
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span>View on Pexels</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Open this photo on Pexels website</p>
                    </TooltipContent>
                  </Tooltip>
                </CardContent>
              </Card>
              </TooltipTrigger>
              <TooltipContent>
                <p>Share and external actions</p>
              </TooltipContent>
            </Tooltip>

              {/* Attribution */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20 cursor-help">
                    <CardContent className="p-6">
                      <div className="space-y-3">
                        <h4 className="text-sm font-semibold text-card-foreground">Attribution</h4>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          Photo by{' '}
                          <a
                            href={photo.photographer_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:underline font-medium"
                          >
                            {photo.photographer}
                          </a>
                          {' '}on{' '}
                          <a
                            href="https://www.pexels.com"
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:underline font-medium"
                          >
                            Pexels
                          </a>
                        </p>
                        <p className="text-xs text-muted-foreground">
                          This photo is free to use under the Pexels License. No attribution required, but appreciated.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Learn about Pexels License and attribution guidelines</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
        </div>

        {/* High-resolution image viewer */}
        <ImageViewer
          photo={photo}
          isOpen={isImageViewerOpen}
          onClose={() => setIsImageViewerOpen(false)}
        />
      </DialogContent>
    </Dialog>
    </TooltipProvider>
  );
}
