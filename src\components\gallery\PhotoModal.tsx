import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Download, 
  ExternalLink, 
  User, 
  Calendar, 
  Palette, 
  Maximize, 
  Copy,
  Heart,
  Share2
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogOverlay,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import type { PhotoModalProps } from '../../types';
import { downloadImage, copyToClipboard, formatDate } from '../../lib/utils';

export function PhotoModal({ photo, isOpen, onClose }: PhotoModalProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [selectedSize, setSelectedSize] = useState<'original' | 'large' | 'medium' | 'small'>('large');

  useEffect(() => {
    if (isOpen && photo) {
      setIsImageLoaded(false);
      setSelectedSize('large');
    }
  }, [isOpen, photo]);

  if (!photo) return null;

  const handleDownload = (size: keyof typeof photo.src = 'original') => {
    const url = photo.src[size];
    downloadImage(url, `photo-${photo.id}-${size}.jpg`);
  };

  const handleCopyLink = async () => {
    try {
      await copyToClipboard(photo.url);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Photo by ${photo.photographer}`,
          text: photo.alt || `Beautiful photo by ${photo.photographer}`,
          url: photo.url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      handleCopyLink();
    }
  };

  const sizeOptions = [
    { key: 'small' as const, label: 'Small', dimensions: '640×427' },
    { key: 'medium' as const, label: 'Medium', dimensions: '1280×853' },
    { key: 'large' as const, label: 'Large', dimensions: '1920×1280' },
    { key: 'original' as const, label: 'Original', dimensions: `${photo.width}×${photo.height}` },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="bg-black/90 backdrop-blur-sm" />
      <DialogContent className="max-w-7xl w-full h-full max-h-screen p-0 border-0 bg-transparent overflow-hidden">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative w-full h-full flex flex-col lg:flex-row"
        >
          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="absolute top-4 right-4 z-50 bg-black/50 hover:bg-black/70 text-white border-0 h-10 w-10"
          >
            <X className="h-5 w-5" />
          </Button>

          {/* Image container */}
          <div className="flex-1 relative flex items-center justify-center p-4 lg:p-8">
            <motion.div
              layoutId={`photo-${photo.id}`}
              className="relative max-w-full max-h-full"
            >
              {!isImageLoaded && (
                <div className="absolute inset-0 bg-muted animate-pulse rounded-lg" />
              )}
              
              <img
                src={photo.src[selectedSize]}
                alt={photo.alt || `Photo by ${photo.photographer}`}
                className={`max-w-full max-h-full object-contain rounded-lg shadow-2xl transition-opacity duration-300 ${
                  isImageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setIsImageLoaded(true)}
              />
            </motion.div>
          </div>

          {/* Info panel */}
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="w-full lg:w-96 bg-background border-l"
          >
            <div className="h-full overflow-y-auto p-6 space-y-6">
              {/* Photographer info */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <User className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold truncate">{photo.photographer}</h3>
                      <p className="text-sm text-muted-foreground">Photographer</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(photo.photographer_url, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Photo details */}
              <Card>
                <CardContent className="p-4 space-y-4">
                  <h4 className="font-semibold">Photo Details</h4>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Dimensions</span>
                      <span className="text-sm font-medium">{photo.width} × {photo.height}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Photo ID</span>
                      <span className="text-sm font-medium">#{photo.id}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Average Color</span>
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-4 h-4 rounded border"
                          style={{ backgroundColor: photo.avg_color }}
                        />
                        <span className="text-sm font-medium">{photo.avg_color}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Size selector */}
              <Card>
                <CardContent className="p-4 space-y-4">
                  <h4 className="font-semibold">View Size</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {sizeOptions.map((option) => (
                      <Button
                        key={option.key}
                        variant={selectedSize === option.key ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedSize(option.key)}
                        className="flex flex-col items-center p-3 h-auto"
                      >
                        <span className="font-medium">{option.label}</span>
                        <span className="text-xs opacity-70">{option.dimensions}</span>
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Download options */}
              <Card>
                <CardContent className="p-4 space-y-4">
                  <h4 className="font-semibold">Download</h4>
                  <div className="space-y-2">
                    {sizeOptions.map((option) => (
                      <Button
                        key={`download-${option.key}`}
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownload(option.key)}
                        className="w-full justify-between"
                      >
                        <span>{option.label} ({option.dimensions})</span>
                        <Download className="h-4 w-4" />
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardContent className="p-4 space-y-4">
                  <h4 className="font-semibold">Actions</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleShare}
                      className="flex items-center space-x-2"
                    >
                      <Share2 className="h-4 w-4" />
                      <span>Share</span>
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLink}
                      className="flex items-center space-x-2"
                    >
                      <Copy className="h-4 w-4" />
                      <span>Copy Link</span>
                    </Button>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(photo.url, '_blank')}
                    className="w-full flex items-center space-x-2"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span>View on Pexels</span>
                  </Button>
                </CardContent>
              </Card>

              {/* Attribution */}
              <Card className="bg-muted/50">
                <CardContent className="p-4">
                  <p className="text-xs text-muted-foreground">
                    Photo by{' '}
                    <a
                      href={photo.photographer_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      {photo.photographer}
                    </a>
                    {' '}on{' '}
                    <a
                      href="https://www.pexels.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      Pexels
                    </a>
                  </p>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
