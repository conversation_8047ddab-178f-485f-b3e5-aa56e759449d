import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  X,
  Download,
  ExternalLink,
  User,
  Copy,
  Share2,
  Eye,
  Camera
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogOverlay,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import type { PhotoModalProps } from '../../types';
import { downloadImage, copyToClipboard } from '../../lib/utils';

export function PhotoModal({ photo, isOpen, onClose }: PhotoModalProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [selectedSize, setSelectedSize] = useState<'original' | 'large' | 'medium' | 'small'>('large');

  useEffect(() => {
    if (isOpen && photo) {
      setIsImageLoaded(false);
      setSelectedSize('large');
    }
  }, [isOpen, photo]);

  if (!photo) return null;

  const handleDownload = (size: keyof typeof photo.src = 'original') => {
    const url = photo.src[size];
    downloadImage(url, `photo-${photo.id}-${size}.jpg`);
  };

  const handleCopyLink = async () => {
    try {
      await copyToClipboard(photo.url);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Photo by ${photo.photographer}`,
          text: photo.alt || `Beautiful photo by ${photo.photographer}`,
          url: photo.url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      handleCopyLink();
    }
  };

  const sizeOptions = [
    { key: 'small' as const, label: 'Small', dimensions: '640×427' },
    { key: 'medium' as const, label: 'Medium', dimensions: '1280×853' },
    { key: 'large' as const, label: 'Large', dimensions: '1920×1280' },
    { key: 'original' as const, label: 'Original', dimensions: `${photo.width}×${photo.height}` },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="bg-black/95 backdrop-blur-sm" />
      <DialogContent className="max-w-[95vw] w-full h-[95vh] p-0 border-0 bg-transparent overflow-auto">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="relative w-full h-full flex flex-col lg:flex-row bg-background rounded-lg overflow-hidden"
        >
          {/* Close button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="absolute top-4 right-4 z-50 bg-background/80 hover:bg-background text-foreground border h-10 w-10 backdrop-blur-sm"
          >
            <X className="h-5 w-5" />
          </Button>

          {/* Image container */}
          <div className="flex-1 relative flex items-start justify-center p-4 lg:p-6 overflow-auto">
            <motion.div
              layoutId={`photo-${photo.id}`}
              className="relative w-full max-w-4xl"
            >
              {!isImageLoaded && (
                <div className="w-full aspect-[4/3] bg-muted animate-pulse rounded-lg" />
              )}

              <img
                src={photo.src[selectedSize]}
                alt={photo.alt || `Photo by ${photo.photographer}`}
                className={`w-full h-auto object-contain rounded-lg shadow-2xl transition-opacity duration-300 ${
                  isImageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setIsImageLoaded(true)}
                style={{ maxHeight: 'none' }}
              />

              {/* Mobile info section */}
              <div className="lg:hidden mt-6 space-y-4">
                {/* Photographer info */}
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="h-12 w-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold">{photo.photographer}</h3>
                        <p className="text-sm text-muted-foreground">Professional Photographer</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(photo.photographer_url, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                    {photo.alt && (
                      <p className="text-sm text-muted-foreground">{photo.alt}</p>
                    )}
                  </CardContent>
                </Card>

                {/* Quick actions */}
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => handleDownload('original')}
                    className="flex items-center justify-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>Download</span>
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleShare}
                    className="flex items-center justify-center space-x-2"
                  >
                    <Share2 className="h-4 w-4" />
                    <span>Share</span>
                  </Button>
                </div>

                {/* Photo details */}
                <Card>
                  <CardContent className="p-4">
                    <h4 className="font-semibold mb-3">Photo Details</h4>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div>
                        <span className="text-muted-foreground">Dimensions</span>
                        <p className="font-medium">{photo.width} × {photo.height}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Photo ID</span>
                        <p className="font-medium">#{photo.id}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Color</span>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-4 h-4 rounded border"
                            style={{ backgroundColor: photo.avg_color }}
                          />
                          <span className="font-medium text-xs">{photo.avg_color}</span>
                        </div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Orientation</span>
                        <p className="font-medium capitalize">
                          {photo.width > photo.height ? 'Landscape' : photo.height > photo.width ? 'Portrait' : 'Square'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </motion.div>
          </div>

          {/* Info panel */}
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="hidden lg:flex lg:w-96 bg-background border-l flex-col"
          >
            <div className="h-full overflow-y-auto p-6 space-y-6">
              {/* Photographer info */}
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-4">
                      <div className="h-16 w-16 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full flex items-center justify-center">
                        <User className="h-8 w-8 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold truncate">{photo.photographer}</h3>
                        <p className="text-sm text-muted-foreground">Professional Photographer</p>
                        <p className="text-xs text-muted-foreground">ID: {photo.photographer_id}</p>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(photo.photographer_url, '_blank')}
                        className="flex-1"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Profile
                      </Button>
                    </div>

                    {/* Photo description */}
                    {photo.alt && (
                      <div className="pt-4 border-t">
                        <h4 className="text-sm font-medium mb-2">Description</h4>
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {photo.alt}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Photo details */}
              <Card>
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Camera className="h-4 w-4" />
                    Photo Details
                  </h4>

                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Dimensions</span>
                      <span className="text-sm font-medium">{photo.width} × {photo.height}</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Photo ID</span>
                      <span className="text-sm font-medium">#{photo.id}</span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Average Color</span>
                      <div className="flex items-center space-x-2">
                        <div
                          className="w-6 h-6 rounded-md border-2 border-white shadow-sm"
                          style={{ backgroundColor: photo.avg_color }}
                        />
                        <span className="text-sm font-medium font-mono">{photo.avg_color}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Aspect Ratio</span>
                      <span className="text-sm font-medium">
                        {(photo.width / photo.height).toFixed(2)}:1
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <span className="text-sm text-muted-foreground">Orientation</span>
                      <span className="text-sm font-medium capitalize">
                        {photo.width > photo.height ? 'Landscape' : photo.height > photo.width ? 'Portrait' : 'Square'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Size selector */}
              <Card>
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    View Size
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    {sizeOptions.map((option) => (
                      <Button
                        key={option.key}
                        variant={selectedSize === option.key ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedSize(option.key)}
                        className="flex flex-col items-center p-4 h-auto transition-all"
                      >
                        <span className="font-medium">{option.label}</span>
                        <span className="text-xs opacity-70">{option.dimensions}</span>
                      </Button>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Currently viewing: <span className="font-medium">{selectedSize}</span> size
                  </p>
                </CardContent>
              </Card>

              {/* Download options */}
              <Card>
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Download Options
                  </h4>
                  <div className="space-y-3">
                    {sizeOptions.map((option) => (
                      <Button
                        key={`download-${option.key}`}
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownload(option.key)}
                        className="w-full justify-between p-4 h-auto hover:bg-primary/5"
                      >
                        <div className="text-left">
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">{option.dimensions}</div>
                        </div>
                        <Download className="h-4 w-4" />
                      </Button>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Free to use under Pexels License
                  </p>
                </CardContent>
              </Card>

              {/* Actions */}
              <Card>
                <CardContent className="p-6 space-y-4">
                  <h4 className="font-semibold">Share & Actions</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleShare}
                      className="flex items-center justify-center space-x-2 p-3"
                    >
                      <Share2 className="h-4 w-4" />
                      <span>Share</span>
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLink}
                      className="flex items-center justify-center space-x-2 p-3"
                    >
                      <Copy className="h-4 w-4" />
                      <span>Copy Link</span>
                    </Button>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open(photo.url, '_blank')}
                    className="w-full flex items-center justify-center space-x-2 p-3"
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span>View on Pexels</span>
                  </Button>
                </CardContent>
              </Card>

              {/* Attribution */}
              <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold">Attribution</h4>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      Photo by{' '}
                      <a
                        href={photo.photographer_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline font-medium"
                      >
                        {photo.photographer}
                      </a>
                      {' '}on{' '}
                      <a
                        href="https://www.pexels.com"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline font-medium"
                      >
                        Pexels
                      </a>
                    </p>
                    <p className="text-xs text-muted-foreground">
                      This photo is free to use under the Pexels License. No attribution required, but appreciated.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
