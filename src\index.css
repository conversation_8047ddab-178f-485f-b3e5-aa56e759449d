@import "tailwindcss";

/* Custom CSS Variables for ShadCN UI */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  }
}

/* Custom scrollbars */
@layer base {
  /* Webkit browsers (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }
}

/* Remove scroll animations and add utilities */
@layer utilities {
  .no-scroll-animation * {
    scroll-behavior: auto !important;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .elegant-scroll {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.2) transparent;
  }

  .elegant-scroll::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .elegant-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .elegant-scroll::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.2);
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .elegant-scroll::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.4);
  }

  /* Grid utilities for consistent layout */
  .grid-consistent {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  @media (max-width: 640px) {
    .grid-consistent {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
    }
  }

  .card-consistent {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .aspect-square-strict {
    aspect-ratio: 1 / 1;
    width: 100%;
    height: auto;
  }

  /* High quality image rendering */
  .high-quality-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
  }

  /* Prevent image corruption during transforms */
  .transform-gpu {
    will-change: transform;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  /* PhotoModal unified positioning - No shifting approach */
  .photo-modal-unified[data-radix-dialog-content] {
    /* Completely override ALL Dialog defaults */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    border-radius: 0 !important;
    transform: none !important;
    translate: none !important;
    inset: 0 !important;
    /* Layout */
    display: flex !important;
    flex-direction: column !important;
    /* Remove default grid */
    grid-template-columns: none !important;
    gap: 0 !important;
  }

  /* Mobile layout (default) */
  .photo-modal-mobile-content {
    height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .photo-modal-image-mobile {
    height: 50vh !important;
    min-height: 50vh !important;
    max-height: 50vh !important;
    flex: none !important;
  }

  .photo-modal-info-mobile {
    height: 50vh !important;
    min-height: 50vh !important;
    max-height: 50vh !important;
    flex: none !important;
    overflow-y: auto !important;
    display: block !important;
  }

  /* Desktop adjustments */
  @media (min-width: 768px) {
    .photo-modal-unified[data-radix-dialog-content] {
      /* Desktop: centered with margins */
      top: 5vh !important;
      left: 5vw !important;
      right: 5vw !important;
      bottom: 5vh !important;
      width: 90vw !important;
      height: 90vh !important;
      border: 1px solid hsl(var(--border)) !important;
      border-radius: 0.5rem !important;
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
    }

    .photo-modal-mobile-content {
      flex-direction: row !important;
      height: 100% !important;
    }

    .photo-modal-image-mobile {
      flex: 1 !important;
      height: 100% !important;
      min-height: 0 !important;
      max-height: none !important;
    }

    .photo-modal-info-mobile {
      display: none !important;
    }
  }

  /* PhotoModal z-index fixes - Lower than ImageViewer */
  [data-radix-dialog-overlay] {
    z-index: 40 !important;
  }

  [data-radix-dialog-content] {
    z-index: 45 !important;
  }

  /* Override default Dialog positioning for PhotoModal */
  .photo-modal-container[data-radix-dialog-content] {
    /* Reset all default positioning and layout */
    grid-template-columns: none !important;
    gap: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    position: fixed !important;
    /* Override default centering */
    top: auto !important;
    left: auto !important;
    right: auto !important;
    bottom: auto !important;
    transform: none !important;
    translate: none !important;
    /* Override default sizing */
    width: auto !important;
    height: auto !important;
    max-width: none !important;
    max-height: none !important;
  }

  /* Enhanced backdrop blur support */
  @supports (backdrop-filter: blur(12px)) {
    .backdrop-blur-md {
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
    }
  }
}
