@import "tailwindcss";

/* Custom CSS Variables for ShadCN UI */
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 262 83% 58%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  }
}

/* Custom scrollbars */
@layer base {
  /* Webkit browsers (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  ::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Firefox */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }
}

/* Remove scroll animations and add utilities */
@layer utilities {
  .no-scroll-animation * {
    scroll-behavior: auto !important;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  .elegant-scroll {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.2) transparent;
  }

  .elegant-scroll::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .elegant-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .elegant-scroll::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.2);
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .elegant-scroll::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.4);
  }

  /* Grid utilities for consistent layout */
  .grid-consistent {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  @media (max-width: 640px) {
    .grid-consistent {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 1rem;
    }
  }

  .card-consistent {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .aspect-square-strict {
    aspect-ratio: 1 / 1;
    width: 100%;
    height: auto;
  }

  /* High quality image rendering */
  .high-quality-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
  }

  /* Prevent image corruption during transforms */
  .transform-gpu {
    will-change: transform;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  /* PhotoModal responsive utilities */
  @media (max-width: 767px) {
    /* Mobile modal styling - Full screen */
    .photo-modal-mobile {
      width: 100vw !important;
      height: 100vh !important;
      max-width: 100vw !important;
      max-height: 100vh !important;
      border-radius: 0 !important;
      margin: 0 !important;
      top: 0 !important;
      left: 0 !important;
      transform: none !important;
      position: fixed !important;
    }

    .photo-modal-mobile-content {
      border-radius: 0 !important;
      height: 100vh !important;
      display: flex !important;
      flex-direction: column !important;
    }

    /* Mobile image container - Top half */
    .photo-modal-image-mobile {
      height: 50vh !important;
      min-height: 50vh !important;
      max-height: 50vh !important;
      flex: none !important;
    }

    /* Mobile info section - Bottom half */
    .photo-modal-info-mobile {
      height: 50vh !important;
      min-height: 50vh !important;
      max-height: 50vh !important;
      flex: none !important;
      overflow-y: auto !important;
      display: block !important;
    }
  }

  /* Desktop and Tablet styling */
  @media (min-width: 768px) {
    .photo-modal-desktop {
      width: 90vw !important;
      height: 90vh !important;
      max-width: 90vw !important;
      max-height: 90vh !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
    }

    .photo-modal-mobile-content {
      flex-direction: row !important;
      height: 100% !important;
      display: flex !important;
    }

    /* Desktop image container - Left side, flex-1 */
    .photo-modal-image-mobile {
      flex: 1 !important;
      height: 100% !important;
      min-height: 0 !important;
      max-height: none !important;
    }

    /* Desktop info section - Right sidebar, hidden on mobile */
    .photo-modal-info-mobile {
      display: none !important;
    }
  }

  /* PhotoModal z-index fixes - Lower than ImageViewer */
  [data-radix-dialog-overlay] {
    z-index: 40 !important;
  }

  [data-radix-dialog-content] {
    z-index: 45 !important;
  }

  /* Enhanced backdrop blur support */
  @supports (backdrop-filter: blur(12px)) {
    .backdrop-blur-md {
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
    }
  }
}
