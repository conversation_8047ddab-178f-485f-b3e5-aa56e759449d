import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ZoomIn, ZoomOut, Download, RotateCw } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogOverlay,
} from '../ui/dialog';
import { Button } from '../ui/button';
import type { PexelsPhoto } from '../../types';
import { downloadImage } from '../../lib/utils';

interface ImageViewerProps {
  photo: PexelsPhoto | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ImageViewer({ photo, isOpen, onClose }: ImageViewerProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);

  useEffect(() => {
    if (isOpen && photo) {
      setIsImageLoaded(false);
      setZoom(1);
      setRotation(0);
    }
  }, [isOpen, photo]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case '+':
        case '=':
          e.preventDefault();
          setZoom(prev => Math.min(prev * 1.2, 5));
          break;
        case '-':
          e.preventDefault();
          setZoom(prev => Math.max(prev / 1.2, 0.1));
          break;
        case '0':
          e.preventDefault();
          setZoom(1);
          break;
        case 'r':
          e.preventDefault();
          setRotation(prev => prev + 90);
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose]);

  if (!photo) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="bg-black/95 backdrop-blur-sm" />
      <DialogContent className="max-w-[100vw] w-full h-[100vh] p-0 border-0 bg-transparent overflow-hidden">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="relative w-full h-full bg-black flex items-center justify-center"
        >
          {/* Loading state */}
          <AnimatePresence>
            {!isImageLoaded && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 flex items-center justify-center bg-black"
              >
                <div className="text-center space-y-4">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                    className="w-12 h-12 border-2 border-white/30 border-t-white rounded-full mx-auto"
                  />
                  <div className="text-white/80">
                    <p className="text-lg font-medium">Loading High Resolution Image</p>
                    <p className="text-sm text-white/60">
                      {photo.width} × {photo.height} • {photo.photographer}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Image container */}
          <div className="relative w-full h-full flex items-center justify-center">
            <motion.img
              src={photo.src.original}
              alt={photo.alt || `Photo by ${photo.photographer}`}
              className={`transition-opacity duration-500 ${
                isImageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              style={{
                transform: `scale(${zoom}) rotate(${rotation}deg)`,
                maxWidth: zoom === 1 ? '90vw' : 'none',
                maxHeight: zoom === 1 ? '90vh' : 'none',
                objectFit: 'contain',
              }}
              onLoad={() => setIsImageLoaded(true)}
              draggable={false}
            />
          </div>

          {/* Controls */}
          <div className="absolute top-4 left-4 right-4 flex justify-between items-start z-50">
            {/* Info */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-black/50 backdrop-blur-sm rounded-lg p-3 text-white"
            >
              <p className="font-medium">{photo.photographer}</p>
              <p className="text-sm text-white/70">
                {photo.width} × {photo.height} • Original Quality
              </p>
            </motion.div>

            {/* Close button */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="bg-black/50 hover:bg-black/70 text-white border-0 h-12 w-12 backdrop-blur-sm"
              >
                <X className="h-6 w-6" />
              </Button>
            </motion.div>
          </div>

          {/* Bottom controls */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50"
          >
            <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 flex items-center space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setZoom(prev => Math.max(prev / 1.2, 0.1))}
                className="text-white hover:bg-white/20 h-10 w-10"
                disabled={zoom <= 0.1}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              
              <span className="text-white text-sm min-w-[60px] text-center">
                {Math.round(zoom * 100)}%
              </span>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setZoom(prev => Math.min(prev * 1.2, 5))}
                className="text-white hover:bg-white/20 h-10 w-10"
                disabled={zoom >= 5}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              
              <div className="w-px h-6 bg-white/20" />
              
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setRotation(prev => prev + 90)}
                className="text-white hover:bg-white/20 h-10 w-10"
              >
                <RotateCw className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setZoom(1);
                  setRotation(0);
                }}
                className="text-white hover:bg-white/20 h-10 w-10"
                title="Reset"
              >
                <span className="text-xs font-bold">1:1</span>
              </Button>
              
              <div className="w-px h-6 bg-white/20" />
              
              <Button
                variant="ghost"
                size="icon"
                onClick={() => downloadImage(photo.src.original, `photo-${photo.id}-original.jpg`)}
                className="text-white hover:bg-white/20 h-10 w-10"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </motion.div>

          {/* Keyboard shortcuts hint */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 2 }}
            className="absolute bottom-4 right-4 bg-black/30 backdrop-blur-sm rounded-lg p-3 text-white/60 text-xs"
          >
            <p>Use +/- to zoom, R to rotate, 0 to reset</p>
          </motion.div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
