import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ZoomIn, ZoomOut, Download, RotateCw } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogOverlay,
} from '../ui/dialog';
import { Button } from '../ui/button';
import type { PexelsPhoto } from '../../types';
import { downloadImage } from '../../lib/utils';

interface ImageViewerProps {
  photo: PexelsPhoto | null;
  isOpen: boolean;
  onClose: () => void;
}

export function ImageViewer({ photo, isOpen, onClose }: ImageViewerProps) {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (isOpen && photo) {
      setIsImageLoaded(false);
      setZoom(1);
      setRotation(0);
      setPosition({ x: 0, y: 0 });
      setIsDragging(false);
    }
  }, [isOpen, photo]);

  // Helper function for center-based zooming that maintains the same visual center
  const zoomFromCenter = useCallback((newZoom: number) => {
    if (newZoom !== zoom && newZoom >= 0.1 && newZoom <= 5) {
      // When zoom is 1, position should always be (0, 0) to center the image
      if (newZoom === 1) {
        setPosition({ x: 0, y: 0 });
        setZoom(newZoom);
      } else {
        // For other zoom levels, maintain the current visual center
        // The position should scale inversely with zoom to maintain the same visual point
        const zoomRatio = newZoom / zoom;
        setPosition(prev => ({
          x: prev.x * zoomRatio,
          y: prev.y * zoomRatio
        }));
        setZoom(newZoom);
      }
    }
  }, [zoom]);

  // Zoom in function
  const zoomIn = useCallback(() => {
    zoomFromCenter(zoom * 1.05);
  }, [zoom, zoomFromCenter]);

  // Zoom out function
  const zoomOut = useCallback(() => {
    zoomFromCenter(zoom * 0.95);
  }, [zoom, zoomFromCenter]);

  // Function to recenter the image at current zoom level
  const recenterImage = useCallback(() => {
    if (zoom === 1) {
      setPosition({ x: 0, y: 0 });
    }
  }, [zoom]);

  // Double-click to reset zoom and center
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    zoomFromCenter(1);
    setRotation(0);
  }, [zoomFromCenter]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case '+':
        case '=':
          e.preventDefault();
          zoomIn();
          break;
        case '-':
          e.preventDefault();
          zoomOut();
          break;
        case '0':
          e.preventDefault();
          // Use zoomFromCenter to ensure proper reset
          zoomFromCenter(1);
          break;
        case 'r':
          e.preventDefault();
          setRotation(prev => prev + 90);
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose, zoomIn, zoomOut]);

  // Mouse wheel zoom from center
  const handleWheel = useCallback((e: WheelEvent) => {
    if (!isOpen) return;
    e.preventDefault();

    const delta = e.deltaY > 0 ? 0.95 : 1.05; // 5% increment/decrement
    const newZoom = Math.max(0.1, Math.min(5, zoom * delta));

    // Use the center-based zoom function for consistency
    zoomFromCenter(newZoom);
  }, [isOpen, zoom, zoomFromCenter]);

  // Mouse drag for panning
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (zoom > 1) {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    }
  }, [zoom, position]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging && zoom > 1) {
      e.preventDefault();
      const newPosition = {
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      };
      setPosition(newPosition);
    }
  }, [isDragging, zoom, dragStart]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Add wheel event listener
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('wheel', handleWheel, { passive: false });
      return () => document.removeEventListener('wheel', handleWheel);
    }
  }, [isOpen, handleWheel]);



  if (!photo) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogOverlay className="bg-black/95 backdrop-blur-sm" />
      <DialogContent className="max-w-[100vw] w-full h-[100vh] p-0 border-0 bg-transparent overflow-hidden">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="relative w-full h-full bg-black flex items-center justify-center"
        >
          {/* Loading state */}
          <AnimatePresence>
            {!isImageLoaded && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 flex items-center justify-center bg-black"
              >
                <div className="text-center space-y-4">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                    className="w-12 h-12 border-2 border-white/30 border-t-white rounded-full mx-auto"
                  />
                  <div className="text-white/80">
                    <p className="text-lg font-medium">Loading High Resolution Image</p>
                    <p className="text-sm text-white/60">
                      {photo.width} × {photo.height} • {photo.photographer}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Image container */}
          <div
            className={`relative w-full h-full flex items-center justify-center ${
              zoom > 1 ? 'cursor-grab active:cursor-grabbing' : 'cursor-default'
            }`}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onDoubleClick={handleDoubleClick}
          >
            <motion.img
              src={photo.src.original}
              alt={photo.alt || `Photo by ${photo.photographer}`}
              className={`transition-opacity duration-500 ${
                isImageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              style={{
                transform: `translate(${position.x}px, ${position.y}px) scale(${zoom}) rotate(${rotation}deg)`,
                transformOrigin: 'center center',
                maxWidth: zoom === 1 ? '90vw' : 'none',
                maxHeight: zoom === 1 ? '90vh' : 'none',
                objectFit: 'contain',
              }}
              onLoad={() => setIsImageLoaded(true)}
              draggable={false}
            />
          </div>

          {/* Controls - Fixed positioning */}
          <div className="fixed top-4 left-4 right-4 flex justify-between items-start z-50 pointer-events-none">
            {/* Info */}
            <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3 text-white pointer-events-auto">
              <p className="font-medium">{photo.photographer}</p>
              <p className="text-sm text-white/70">
                {photo.width} × {photo.height} • Original Quality
              </p>
            </div>

            {/* Close button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="bg-black/50 hover:bg-black/70 text-white border-0 h-12 w-12 backdrop-blur-sm pointer-events-auto"
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          {/* Bottom controls - Fixed positioning */}
          <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
            <div className="bg-black/50 backdrop-blur-sm rounded-lg p-2 flex items-center space-x-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={zoomOut}
                className="text-white hover:bg-white/20 h-10 w-10"
                disabled={zoom <= 0.1}
                title="Zoom Out (5%)"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>

              <span className="text-white text-sm min-w-[60px] text-center font-mono">
                {Math.round(zoom * 100)}%
              </span>

              <Button
                variant="ghost"
                size="icon"
                onClick={zoomIn}
                className="text-white hover:bg-white/20 h-10 w-10"
                disabled={zoom >= 5}
                title="Zoom In (5%)"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>

              <div className="w-px h-6 bg-white/20" />

              <Button
                variant="ghost"
                size="icon"
                onClick={() => setRotation(prev => prev + 90)}
                className="text-white hover:bg-white/20 h-10 w-10"
                title="Rotate 90°"
              >
                <RotateCw className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  zoomFromCenter(1);
                  setRotation(0);
                }}
                className="text-white hover:bg-white/20 h-10 w-10"
                title="Reset View"
              >
                <span className="text-xs font-bold">1:1</span>
              </Button>

              <div className="w-px h-6 bg-white/20" />

              <Button
                variant="ghost"
                size="icon"
                onClick={() => downloadImage(photo.src.original, `photo-${photo.id}-original.jpg`)}
                className="text-white hover:bg-white/20 h-10 w-10"
                title="Download Original"
              >
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Keyboard shortcuts hint - Fixed positioning */}
          <div className="fixed bottom-4 right-4 bg-black/30 backdrop-blur-sm rounded-lg p-3 text-white/60 text-xs">
            <p>Use +/- to zoom (5%), R to rotate, 0 to reset</p>
            <p className="mt-1">Mouse wheel to zoom • Drag to pan when zoomed</p>
            <p className="mt-1">Double-click to reset view</p>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
