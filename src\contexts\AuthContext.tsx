import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { generateId } from '../lib/utils';
import type {
  User,
  AuthState,
  LoginCredentials,
  SignupCredentials,
  StoredUser
} from '../types';
import { LOCAL_STORAGE_KEYS } from '../types';

interface AuthContextType {
  state: AuthState;
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (credentials: SignupCredentials) => Promise<void>;
  logout: () => void;
  incrementSearchCount: () => void;
}

type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: User }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'INCREMENT_SEARCH_COUNT' };

const initialState: AuthState = {
  user: null,
  isLoading: false,
  error: null,
};

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        isLoading: false,
        user: action.payload,
        error: null,
      };
    case 'AUTH_ERROR':
      return {
        ...state,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        error: null,
      };
    case 'INCREMENT_SEARCH_COUNT':
      return {
        ...state,
        user: state.user ? {
          ...state.user,
          searchCount: state.user.searchCount + 1,
        } : null,
      };
    default:
      return state;
  }
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

// Simple in-memory user storage (in a real app, this would be a backend)
const users: Map<string, { username: string; password: string; user: StoredUser }> = new Map();

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user from localStorage on mount
  useEffect(() => {
    const storedUser = localStorage.getItem(LOCAL_STORAGE_KEYS.USER);
    if (storedUser) {
      try {
        const userData: StoredUser = JSON.parse(storedUser);
        const user: User = {
          ...userData,
          createdAt: new Date(userData.createdAt),
          isAuthenticated: true,
        };
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
      } catch (error) {
        console.error('Failed to parse stored user data:', error);
        localStorage.removeItem(LOCAL_STORAGE_KEYS.USER);
      }
    }
  }, []);

  // Save user to localStorage whenever user state changes
  useEffect(() => {
    if (state.user) {
      const storedUser: StoredUser = {
        id: state.user.id,
        username: state.user.username,
        email: state.user.email,
        createdAt: state.user.createdAt.toISOString(),
        searchCount: state.user.searchCount,
      };
      localStorage.setItem(LOCAL_STORAGE_KEYS.USER, JSON.stringify(storedUser));
    } else {
      localStorage.removeItem(LOCAL_STORAGE_KEYS.USER);
    }
  }, [state.user]);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    dispatch({ type: 'AUTH_START' });

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const userRecord = users.get(credentials.username);
      if (!userRecord || userRecord.password !== credentials.password) {
        throw new Error('Invalid username or password');
      }

      const user: User = {
        ...userRecord.user,
        createdAt: new Date(userRecord.user.createdAt),
        isAuthenticated: true,
      };

      dispatch({ type: 'AUTH_SUCCESS', payload: user });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  };

  const signup = async (credentials: SignupCredentials): Promise<void> => {
    dispatch({ type: 'AUTH_START' });

    try {
      // Validate credentials
      if (credentials.password !== credentials.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      if (credentials.password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      if (credentials.username.length < 3) {
        throw new Error('Username must be at least 3 characters long');
      }

      // Check if username already exists
      if (users.has(credentials.username)) {
        throw new Error('Username already exists');
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const userId = generateId();
      const now = new Date();

      const storedUser: StoredUser = {
        id: userId,
        username: credentials.username,
        createdAt: now.toISOString(),
        searchCount: 0,
      };

      const user: User = {
        ...storedUser,
        createdAt: now,
        isAuthenticated: true,
      };

      // Store user in memory
      users.set(credentials.username, {
        username: credentials.username,
        password: credentials.password,
        user: storedUser,
      });

      dispatch({ type: 'AUTH_SUCCESS', payload: user });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Signup failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  };

  const logout = (): void => {
    dispatch({ type: 'LOGOUT' });
  };

  const incrementSearchCount = (): void => {
    dispatch({ type: 'INCREMENT_SEARCH_COUNT' });
  };

  const value: AuthContextType = {
    state,
    login,
    signup,
    logout,
    incrementSearchCount,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
