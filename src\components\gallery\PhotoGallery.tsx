import React, { useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, AlertCircle, ImageOff } from 'lucide-react';
import { PhotoCard } from './PhotoCard';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { PexelsPhoto } from '../../types';

interface PhotoGalleryProps {
  photos: PexelsPhoto[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  onLoadMore: () => void;
  onPhotoClick: (photo: PexelsPhoto) => void;
  searchQuery?: string;
}

export function PhotoGallery({
  photos,
  isLoading,
  error,
  hasMore,
  onLoadMore,
  onPhotoClick,
  searchQuery
}: PhotoGalleryProps) {
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for infinite scroll
  const handleObserver = useCallback((entries: IntersectionObserverEntry[]) => {
    const target = entries[0];
    if (target.isIntersecting && hasMore && !isLoading) {
      onLoadMore();
    }
  }, [hasMore, isLoading, onLoadMore]);

  useEffect(() => {
    const option = {
      root: null,
      rootMargin: '100px',
      threshold: 0
    };
    
    const observer = new IntersectionObserver(handleObserver, option);
    const currentRef = loadMoreRef.current;
    
    if (currentRef) observer.observe(currentRef);
    
    return () => {
      if (currentRef) observer.unobserve(currentRef);
    };
  }, [handleObserver]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  // Error state
  if (error && photos.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col items-center justify-center py-16 px-4"
      >
        <Card className="p-8 text-center max-w-md">
          <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Something went wrong</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </Card>
      </motion.div>
    );
  }

  // No results state
  if (!isLoading && photos.length === 0 && searchQuery) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col items-center justify-center py-16 px-4"
      >
        <Card className="p-8 text-center max-w-md">
          <ImageOff className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No photos found</h3>
          <p className="text-muted-foreground mb-4">
            Sorry, we couldn't find any photos for "{searchQuery}". Try searching with different keywords.
          </p>
          <div className="text-sm text-muted-foreground">
            <p>Try searching for:</p>
            <div className="flex flex-wrap gap-2 mt-2 justify-center">
              {['nature', 'landscape', 'portrait', 'city', 'abstract'].map((suggestion) => (
                <Button
                  key={suggestion}
                  variant="outline"
                  size="sm"
                  onClick={() => onPhotoClick({ id: 0 } as PexelsPhoto)} // This would trigger a new search
                  className="text-xs"
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        </Card>
      </motion.div>
    );
  }

  return (
    <div className="w-full">
      {/* Gallery Grid */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6 p-4 md:p-6"
      >
        <AnimatePresence>
          {photos.map((photo, index) => (
            <motion.div
              key={photo.id}
              variants={itemVariants}
              layout
              layoutId={`photo-${photo.id}`}
              style={{
                // Masonry-like effect with varying heights
                gridRowEnd: `span ${Math.floor(Math.random() * 2) + 1}`
              }}
            >
              <PhotoCard
                photo={photo}
                onPhotoClick={onPhotoClick}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Loading indicator for infinite scroll */}
      <div ref={loadMoreRef} className="flex justify-center py-8">
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center space-x-2 text-muted-foreground"
          >
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading more photos...</span>
          </motion.div>
        )}
        
        {!isLoading && hasMore && photos.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Button
              onClick={onLoadMore}
              variant="outline"
              className="flex items-center space-x-2"
            >
              <span>Load More Photos</span>
            </Button>
          </motion.div>
        )}
        
        {!hasMore && photos.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center text-muted-foreground"
          >
            <p>You've reached the end of the results</p>
          </motion.div>
        )}
      </div>

      {/* Error indicator for additional loads */}
      {error && photos.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-center py-4"
        >
          <Card className="p-4 bg-destructive/10 border-destructive/20">
            <div className="flex items-center space-x-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
