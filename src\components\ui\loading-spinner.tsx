import React from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import type { LoadingSpinnerProps } from '../../types';
import { cn } from '../../lib/utils';

export function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={cn('flex items-center justify-center', className)}
    >
      <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
    </motion.div>
  );
}

export function FullPageLoader() {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-card p-8 rounded-lg shadow-lg border"
      >
        <div className="flex flex-col items-center space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </motion.div>
    </div>
  );
}

export function PhotoGridSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6 p-4 md:p-6">
      {Array.from({ length: 15 }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.05 }}
          className="aspect-[3/4] bg-muted rounded-lg animate-pulse"
        />
      ))}
    </div>
  );
}

export function SearchSkeleton() {
  return (
    <div className="w-full max-w-2xl mx-auto space-y-4">
      <div className="h-12 bg-muted rounded-lg animate-pulse" />
      <div className="h-4 bg-muted rounded w-1/3 mx-auto animate-pulse" />
    </div>
  );
}
