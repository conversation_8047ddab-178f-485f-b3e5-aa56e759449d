// Like functionality service for photos
import React from 'react';
import { LOCAL_STORAGE_KEYS } from '../types';

const LIKED_PHOTOS_KEY = 'photosnap_liked_photos';

export class LikeService {
  private static instance: LikeService;
  private likedPhotos: Set<number>;
  private listeners: Set<(likedPhotos: Set<number>) => void>;

  private constructor() {
    this.likedPhotos = new Set();
    this.listeners = new Set();
    this.loadLikedPhotos();
  }

  static getInstance(): LikeService {
    if (!LikeService.instance) {
      LikeService.instance = new LikeService();
    }
    return LikeService.instance;
  }

  private loadLikedPhotos(): void {
    try {
      const stored = localStorage.getItem(LIKED_PHOTOS_KEY);
      if (stored) {
        const photoIds = JSON.parse(stored) as number[];
        this.likedPhotos = new Set(photoIds);
      }
    } catch (error) {
      console.warn('Failed to load liked photos from localStorage:', error);
      this.likedPhotos = new Set();
    }
  }

  private saveLikedPhotos(): void {
    try {
      const photoIds = Array.from(this.likedPhotos);
      localStorage.setItem(LIKED_PHOTOS_KEY, JSON.stringify(photoIds));
    } catch (error) {
      console.warn('Failed to save liked photos to localStorage:', error);
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(new Set(this.likedPhotos)));
  }

  isLiked(photoId: number): boolean {
    return this.likedPhotos.has(photoId);
  }

  toggleLike(photoId: number): boolean {
    if (this.likedPhotos.has(photoId)) {
      this.likedPhotos.delete(photoId);
    } else {
      this.likedPhotos.add(photoId);
    }
    
    this.saveLikedPhotos();
    this.notifyListeners();
    return this.likedPhotos.has(photoId);
  }

  getLikedPhotos(): Set<number> {
    return new Set(this.likedPhotos);
  }

  getLikedCount(): number {
    return this.likedPhotos.size;
  }

  subscribe(listener: (likedPhotos: Set<number>) => void): () => void {
    this.listeners.add(listener);
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener);
    };
  }

  clearAllLikes(): void {
    this.likedPhotos.clear();
    this.saveLikedPhotos();
    this.notifyListeners();
  }
}

// Export singleton instance
export const likeService = LikeService.getInstance();

// React hook for using like functionality
export function useLikes() {
  const [likedPhotos, setLikedPhotos] = React.useState<Set<number>>(
    likeService.getLikedPhotos()
  );

  React.useEffect(() => {
    const unsubscribe = likeService.subscribe(setLikedPhotos);
    return unsubscribe;
  }, []);

  const toggleLike = React.useCallback((photoId: number) => {
    return likeService.toggleLike(photoId);
  }, []);

  const isLiked = React.useCallback((photoId: number) => {
    return likeService.isLiked(photoId);
  }, []);

  return {
    likedPhotos,
    toggleLike,
    isLiked,
    likedCount: likedPhotos.size,
  };
}
