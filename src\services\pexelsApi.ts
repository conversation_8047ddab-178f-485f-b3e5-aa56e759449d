import { PexelsResponse, PexelsSearchResponse, PexelsCuratedResponse, SearchOptions, ApiResponse } from '../types';
import { retry } from '../lib/utils';

// Use the API key from the original project
const PEXELS_API_KEY = 'moCu4MPWpdRACmO3Z5CazFZsnIIkhRKctVNRV9qYU1600SCYpbBxN9Qp';
const BASE_URL = 'https://api.pexels.com/v1';

class PexelsApiService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  private getHeaders(): HeadersInit {
    return {
      'Authorization': PEXELS_API_KEY,
      'Content-Type': 'application/json',
    };
  }

  private getCacheKey(endpoint: string, params: Record<string, any>): string {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key];
        return result;
      }, {} as Record<string, any>);
    
    return `${endpoint}:${JSON.stringify(sortedParams)}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data as T;
    }
    return null;
  }

  private setCache<T>(key: string, data: T): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private async makeRequest<T>(
    endpoint: string,
    params: Record<string, any> = {}
  ): Promise<ApiResponse<T>> {
    const cacheKey = this.getCacheKey(endpoint, params);
    
    // Check cache first
    const cached = this.getFromCache<T>(cacheKey);
    if (cached) {
      return { success: true, data: cached };
    }

    try {
      const url = new URL(`${BASE_URL}${endpoint}`);
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value.toString());
        }
      });

      const response = await retry(async () => {
        const res = await fetch(url.toString(), {
          method: 'GET',
          headers: this.getHeaders(),
        });

        if (!res.ok) {
          if (res.status === 429) {
            throw new Error('Rate limit exceeded. Please try again later.');
          }
          if (res.status === 401) {
            throw new Error('Invalid API key.');
          }
          if (res.status === 404) {
            throw new Error('No photos found.');
          }
          throw new Error(`HTTP error! status: ${res.status}`);
        }

        return res;
      }, 3, 1000);

      const data = await response.json();
      
      // Cache the successful response
      this.setCache(cacheKey, data);
      
      return { success: true, data };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An unknown error occurred';
      console.error('Pexels API error:', error);
      return { success: false, error: message };
    }
  }

  async searchPhotos(options: SearchOptions): Promise<ApiResponse<PexelsSearchResponse>> {
    const { query, page = 1, perPage = 15, filters } = options;
    
    if (!query.trim()) {
      return { success: false, error: 'Search query cannot be empty' };
    }

    const params: Record<string, any> = {
      query: query.trim(),
      page,
      per_page: Math.min(perPage, 80), // Pexels max is 80
    };

    // Add filters if provided
    if (filters?.orientation) {
      params.orientation = filters.orientation;
    }
    if (filters?.size) {
      params.size = filters.size;
    }
    if (filters?.color) {
      params.color = filters.color;
    }

    return this.makeRequest<PexelsSearchResponse>('/search', params);
  }

  async getCuratedPhotos(page: number = 1, perPage: number = 15): Promise<ApiResponse<PexelsCuratedResponse>> {
    const params = {
      page,
      per_page: Math.min(perPage, 80), // Pexels max is 80
    };

    return this.makeRequest<PexelsCuratedResponse>('/curated', params);
  }

  async getPhotoById(id: number): Promise<ApiResponse<any>> {
    return this.makeRequest(`/photos/${id}`);
  }

  // Enhanced search with multiple keywords
  async enhancedSearch(
    keywords: string[],
    page: number = 1,
    perPage: number = 15
  ): Promise<ApiResponse<PexelsSearchResponse>> {
    if (keywords.length === 0) {
      return { success: false, error: 'At least one keyword is required' };
    }

    // Join keywords with spaces for better search results
    const query = keywords.join(' ');
    
    return this.searchPhotos({
      query,
      page,
      perPage,
    });
  }

  // Get popular searches (curated photos with different categories)
  async getPopularSearches(): Promise<string[]> {
    return [
      'nature',
      'landscape',
      'portrait',
      'city',
      'abstract',
      'wildlife',
      'architecture',
      'food',
      'travel',
      'technology',
      'business',
      'fashion',
      'art',
      'sports',
      'music',
    ];
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Get cache stats
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

// Export singleton instance
export const pexelsApi = new PexelsApiService();
export default pexelsApi;
