import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, Clock, TrendingUp } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';
import { useAuth } from '../../contexts/AuthContext';
import type { SearchBarProps } from '../../types';
import { debounce, getRandomPlaceholder } from '../../lib/utils';
import { LOCAL_STORAGE_KEYS, SEARCH_LIMITS } from '../../types';

interface SearchSuggestion {
  query: string;
  type: 'history' | 'popular';
}

export function SearchBar({ onSearch, isLoading, disabled }: SearchBarProps) {
  const { state: authState, incrementSearchCount } = useAuth();
  const [query, setQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [placeholder, setPlaceholder] = useState('');
  const [searchCount, setSearchCount] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  const popularSearches = [
    'nature', 'landscape', 'portrait', 'city', 'abstract',
    'wildlife', 'architecture', 'food', 'travel', 'technology'
  ];

  // Load search history and count from localStorage
  useEffect(() => {
    const storedHistory = localStorage.getItem(LOCAL_STORAGE_KEYS.SEARCH_HISTORY);
    if (storedHistory) {
      try {
        const parsed = JSON.parse(storedHistory);
        setSearchHistory(parsed.queries || []);
      } catch (error) {
        console.error('Failed to parse search history:', error);
      }
    }

    // Set random placeholder
    setPlaceholder(getRandomPlaceholder());

    // Load search count for unauthenticated users
    if (!authState.user) {
      const count = parseInt(localStorage.getItem('guest_search_count') || '0');
      setSearchCount(count);
    }
  }, [authState.user]);

  // Update search count when user changes
  useEffect(() => {
    if (authState.user) {
      setSearchCount(authState.user.searchCount);
    } else {
      const count = parseInt(localStorage.getItem('guest_search_count') || '0');
      setSearchCount(count);
    }
  }, [authState.user]);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const canSearch = authState.user || searchCount < SEARCH_LIMITS.UNAUTHENTICATED;

  const saveSearchHistory = (searchQuery: string) => {
    const trimmedQuery = searchQuery.trim().toLowerCase();
    if (!trimmedQuery) return;

    const updatedHistory = [
      trimmedQuery,
      ...searchHistory.filter(item => item !== trimmedQuery)
    ].slice(0, 10); // Keep only last 10 searches

    setSearchHistory(updatedHistory);
    localStorage.setItem(
      LOCAL_STORAGE_KEYS.SEARCH_HISTORY,
      JSON.stringify({
        queries: updatedHistory,
        timestamp: new Date().toISOString(),
      })
    );
  };

  const handleSearch = (searchQuery: string = query) => {
    const trimmedQuery = searchQuery.trim();
    if (!trimmedQuery || isLoading || disabled) return;

    if (!canSearch) {
      // Show auth modal or limit message
      return;
    }

    // Save to history
    saveSearchHistory(trimmedQuery);

    // Increment search count
    if (authState.user) {
      incrementSearchCount();
    } else {
      const newCount = searchCount + 1;
      setSearchCount(newCount);
      localStorage.setItem('guest_search_count', newCount.toString());
    }

    // Perform search
    onSearch(trimmedQuery);
    setShowSuggestions(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    // Show suggestions when typing
    if (value.trim() && !showSuggestions) {
      setShowSuggestions(true);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    handleSearch(suggestion);
  };

  const clearQuery = () => {
    setQuery('');
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const getSuggestions = (): SearchSuggestion[] => {
    const suggestions: SearchSuggestion[] = [];
    
    // Add matching history items
    const matchingHistory = searchHistory
      .filter(item => item.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 5)
      .map(item => ({ query: item, type: 'history' as const }));
    
    suggestions.push(...matchingHistory);

    // Add popular searches if we have space and query is short
    if (suggestions.length < 8 && query.length < 3) {
      const matchingPopular = popularSearches
        .filter(item => 
          item.toLowerCase().includes(query.toLowerCase()) &&
          !suggestions.some(s => s.query === item)
        )
        .slice(0, 8 - suggestions.length)
        .map(item => ({ query: item, type: 'popular' as const }));
      
      suggestions.push(...matchingPopular);
    }

    return suggestions;
  };

  const remainingSearches = authState.user 
    ? Infinity 
    : SEARCH_LIMITS.UNAUTHENTICATED - searchCount;

  return (
    <div className="relative w-full max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={() => query && setShowSuggestions(true)}
            placeholder={placeholder}
            className="pl-10 pr-20 h-12 text-lg"
            disabled={isLoading || disabled || !canSearch}
          />
          
          {query && (
            <button
              type="button"
              onClick={clearQuery}
              className="absolute right-12 top-1/2 transform -translate-y-1/2 p-1 hover:bg-muted rounded-full transition-colors"
            >
              <X className="h-4 w-4 text-muted-foreground" />
            </button>
          )}
          
          <Button
            type="submit"
            size="sm"
            disabled={!query.trim() || isLoading || disabled || !canSearch}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10"
          >
            {isLoading ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                className="h-4 w-4 border-2 border-current border-t-transparent rounded-full"
              />
            ) : (
              'Search'
            )}
          </Button>
        </div>

        {/* Search limit indicator */}
        {!authState.user && (
          <div className="mt-2 text-center">
            <p className="text-sm text-muted-foreground">
              {remainingSearches > 0 ? (
                <>
                  {remainingSearches} search{remainingSearches !== 1 ? 'es' : ''} remaining
                  <span className="ml-2 text-primary">
                    Sign up for unlimited searches!
                  </span>
                </>
              ) : (
                <span className="text-destructive">
                  Search limit reached. Please sign up to continue searching.
                </span>
              )}
            </p>
          </div>
        )}
      </form>

      {/* Search Suggestions */}
      <AnimatePresence>
        {showSuggestions && query && (
          <motion.div
            ref={suggestionsRef}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 z-50 mt-2"
          >
            <Card className="p-2 shadow-lg border">
              <div className="space-y-1">
                {getSuggestions().map((suggestion, index) => (
                  <motion.button
                    key={`${suggestion.type}-${suggestion.query}`}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    onClick={() => handleSuggestionClick(suggestion.query)}
                    className="w-full flex items-center space-x-3 px-3 py-2 text-left hover:bg-muted rounded-md transition-colors"
                  >
                    {suggestion.type === 'history' ? (
                      <Clock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    ) : (
                      <TrendingUp className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    )}
                    <span className="text-sm">{suggestion.query}</span>
                    {suggestion.type === 'popular' && (
                      <span className="text-xs text-muted-foreground ml-auto">Popular</span>
                    )}
                  </motion.button>
                ))}
                
                {getSuggestions().length === 0 && (
                  <div className="px-3 py-2 text-sm text-muted-foreground text-center">
                    No suggestions found
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
