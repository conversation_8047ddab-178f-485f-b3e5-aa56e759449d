// Utility functions for generating photographer avatars and profiles

export function generateAvatarUrl(photographerId: number, photographerName: string): string {
  // Use DiceBear API for consistent, beautiful avatars
  const seed = `${photographerId}-${photographerName.replace(/\s+/g, '-').toLowerCase()}`;
  return `https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}&backgroundColor=b6e3f4,c0aede,d1d4f9,ffd5dc,ffdfbf`;
}

export function generatePhotographerBio(photographerName: string): string {
  const bios = [
    `Professional photographer passionate about capturing life's beautiful moments.`,
    `Visual storyteller with an eye for detail and composition.`,
    `Creative photographer specializing in natural light and authentic emotions.`,
    `Documenting the world through a unique artistic perspective.`,
    `Passionate about photography and sharing inspiring visual content.`,
    `Capturing the beauty in everyday moments and extraordinary scenes.`,
    `Professional photographer with a love for creative expression.`,
    `Visual artist dedicated to creating compelling and meaningful images.`,
  ];
  
  // Use photographer name to consistently select the same bio
  const index = photographerName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % bios.length;
  return bios[index];
}

export function generatePhotographerStats(photographerId: number): {
  totalPhotos: number;
  followers: number;
  following: number;
} {
  // Generate consistent stats based on photographer ID
  const seed = photographerId;
  const totalPhotos = Math.floor((seed % 500) + 50); // 50-550 photos
  const followers = Math.floor((seed % 10000) + 100); // 100-10,100 followers
  const following = Math.floor((seed % 1000) + 50); // 50-1,050 following
  
  return { totalPhotos, followers, following };
}

export function createPhotographerProfile(
  photographerId: number,
  photographerName: string,
  photographerUrl: string
): import('../types').PhotographerProfile {
  const stats = generatePhotographerStats(photographerId);
  
  return {
    id: photographerId,
    name: photographerName,
    url: photographerUrl,
    avatar: generateAvatarUrl(photographerId, photographerName),
    bio: generatePhotographerBio(photographerName),
    totalPhotos: stats.totalPhotos,
    isFollowing: false,
  };
}
