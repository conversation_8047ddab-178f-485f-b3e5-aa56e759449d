// Pexels API Types
export interface PexelsPhoto {
  id: number;
  width: number;
  height: number;
  url: string;
  photographer: string;
  photographer_url: string;
  photographer_id: number;
  avg_color: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
  liked: boolean;
  alt: string;
}

export interface PexelsResponse {
  page: number;
  per_page: number;
  photos: PexelsPhoto[];
  total_results: number;
  next_page?: string;
  prev_page?: string;
}

export interface PexelsSearchResponse extends PexelsResponse {}

export interface PexelsCuratedResponse extends PexelsResponse {}

// User Authentication Types
export interface User {
  id: string;
  username: string;
  email?: string;
  createdAt: Date;
  searchCount: number;
  isAuthenticated: boolean;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface SignupCredentials {
  username: string;
  password: string;
  confirmPassword: string;
}

// Application State Types
export interface SearchState {
  query: string;
  results: PexelsPhoto[];
  isLoading: boolean;
  error: string | null;
  page: number;
  hasMore: boolean;
  searchHistory: string[];
}

export interface AppState {
  auth: AuthState;
  search: SearchState;
  selectedPhoto: PexelsPhoto | null;
  isPhotoModalOpen: boolean;
}

// Component Props Types
export interface PhotoCardProps {
  photo: PexelsPhoto;
  onPhotoClick: (photo: PexelsPhoto) => void;
}

export interface SearchBarProps {
  onSearch: (query: string) => void;
  isLoading: boolean;
  disabled?: boolean;
}

export interface PhotoModalProps {
  photo: PexelsPhoto | null;
  isOpen: boolean;
  onClose: () => void;
}

export interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'login' | 'signup';
  onModeChange: (mode: 'login' | 'signup') => void;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AuthResponse {
  user: User;
  token?: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}

// Search Types
export interface SearchFilters {
  orientation?: 'landscape' | 'portrait' | 'square';
  size?: 'large' | 'medium' | 'small';
  color?: string;
}

export interface SearchOptions {
  query: string;
  page?: number;
  perPage?: number;
  filters?: SearchFilters;
}

// Local Storage Types
export interface StoredUser {
  id: string;
  username: string;
  email?: string;
  createdAt: string;
  searchCount: number;
}

export interface StoredSearchHistory {
  queries: string[];
  timestamp: string;
}

// Theme Types
export type Theme = 'light' | 'dark' | 'system';

export interface ThemeState {
  theme: Theme;
  systemTheme: 'light' | 'dark';
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Constants
export const SEARCH_LIMITS = {
  UNAUTHENTICATED: 10,
  AUTHENTICATED: Infinity,
} as const;

export const API_ENDPOINTS = {
  PEXELS_BASE: 'https://api.pexels.com/v1',
  SEARCH: '/search',
  CURATED: '/curated',
} as const;

export const LOCAL_STORAGE_KEYS = {
  USER: 'photosnap_user',
  SEARCH_HISTORY: 'photosnap_search_history',
  THEME: 'photosnap_theme',
} as const;
