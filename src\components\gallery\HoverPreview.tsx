import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Heart, Download, ExternalLink, User } from 'lucide-react';
import type { HoverPreviewProps } from '../../types';
import { createPhotographerProfile } from '../../lib/avatarUtils';
import { useLikes } from '../../services/likeService';

export function HoverPreview({ photo, isVisible, position, onPhotoClick }: HoverPreviewProps) {
  const { isLiked, toggleLike } = useLikes();
  const photographerProfile = createPhotographerProfile(
    photo.photographer_id,
    photo.photographer,
    photo.photographer_url
  );

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleLike(photo.id);
  };

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Create a temporary link to download the image
    const link = document.createElement('a');
    link.href = photo.src.large;
    link.download = `photo-${photo.id}-${photo.photographer.replace(/\s+/g, '-').toLowerCase()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePhotoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onPhotoClick(photo);
  };

  const handleProfileClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(photo.photographer_url, '_blank');
  };

  // Determine if preview is on the left or right side
  const isOnLeft = position.x < window.innerWidth / 2;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{
            opacity: 0,
            scale: 0.9,
            x: isOnLeft ? -20 : 20,
            y: 10
          }}
          animate={{
            opacity: 1,
            scale: 1,
            x: 0,
            y: 0
          }}
          exit={{
            opacity: 0,
            scale: 0.9,
            x: isOnLeft ? -20 : 20,
            y: 10
          }}
          transition={{ duration: 0.2, ease: 'easeOut' }}
          className="fixed z-50 pointer-events-auto"
          style={{
            left: position.x,
            top: position.y,
          }}
        >
          <Card className="w-80 bg-gradient-to-br from-white via-white to-gray-50 border shadow-xl overflow-hidden relative">
            {/* Arrow indicator pointing to the hovered image */}
            <div
              className={`absolute top-6 w-0 h-0 ${
                isOnLeft
                  ? 'right-[-8px] border-l-[8px] border-l-border border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent'
                  : 'left-[-8px] border-r-[8px] border-r-border border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent'
              }`}
            />
            <div
              className={`absolute top-6 w-0 h-0 ${
                isOnLeft
                  ? 'right-[-7px] border-l-[7px] border-l-white border-t-[7px] border-t-transparent border-b-[7px] border-b-transparent'
                  : 'left-[-7px] border-r-[7px] border-r-white border-t-[7px] border-t-transparent border-b-[7px] border-b-transparent'
              }`}
            />
            {/* Image Preview */}
            <div 
              className="relative aspect-[4/3] overflow-hidden cursor-pointer group"
              onClick={handlePhotoClick}
            >
              <img
                src={photo.src.medium}
                alt={photo.alt || `Photo by ${photo.photographer}`}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />
              
              {/* Overlay with actions */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300">
                <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="h-8 w-8 p-0 bg-white/90 hover:bg-white text-black"
                    onClick={handleLike}
                  >
                    <Heart 
                      className={`h-4 w-4 ${
                        isLiked(photo.id) ? 'fill-red-500 text-red-500' : ''
                      }`} 
                    />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    className="h-8 w-8 p-0 bg-white/90 hover:bg-white text-black"
                    onClick={handleDownload}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Click to view indicator */}
                <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white/90 text-black text-xs px-2 py-1 rounded flex items-center gap-1">
                    <ExternalLink className="h-3 w-3" />
                    Click to view
                  </div>
                </div>
              </div>
            </div>

            {/* Photo Info */}
            <div className="p-4 space-y-3">
              {/* Photographer Profile */}
              <div
                className="flex items-center gap-3 cursor-pointer hover:bg-gray-100 rounded-lg p-2 -m-2 transition-colors"
                onClick={handleProfileClick}
              >
                <img
                  src={photographerProfile.avatar}
                  alt={photographerProfile.name}
                  className="w-10 h-10 rounded-full bg-muted"
                />
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm truncate">{photographerProfile.name}</p>
                  <p className="text-xs text-muted-foreground truncate">
                    {photographerProfile.totalPhotos} photos
                  </p>
                </div>
                <User className="h-4 w-4 text-muted-foreground" />
              </div>

              {/* Photo Details */}
              <div className="space-y-2">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{photo.width} × {photo.height}</span>
                  <span>ID: {photo.id}</span>
                </div>
                
                {photo.alt && (
                  <p className="text-sm text-foreground line-clamp-2">
                    {photo.alt}
                  </p>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={handlePhotoClick}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Full
                </Button>
                <Button
                  size="sm"
                  variant={isLiked(photo.id) ? "default" : "outline"}
                  onClick={handleLike}
                  className={isLiked(photo.id) ? "bg-red-500 hover:bg-red-600" : ""}
                >
                  <Heart 
                    className={`h-4 w-4 ${
                      isLiked(photo.id) ? 'fill-white' : ''
                    }`} 
                  />
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
