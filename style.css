* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #dfe0e477;
  font-family: "Roboto", sans-serif;
  position: relative;
}

header {
  display: flex;
  flex-direction: column;
  min-height: 30vh;
  justify-content: center;
  align-items: center;
}

header h2 {
  padding: 2rem;
}

#puch-line {
  pointer-events: none;
}

#puch-line:hover {


  text-shadow: 1px 1px 5px #5135a78f;
}

#logo {
  text-decoration: none;
  color: black;
}

#logo:hover {
  text-shadow: 1px 1px 5px #a390db8f;
}

.search-form {
  padding: 2rem;
  display: flex;
}

.search-form input {
  font-size: 2rem;
  padding: 0.5rem;
  width: 100%;
  border: none;
  border: 2px solid rgba(57, 48, 133, 0.493);
  border-right: none;
}

.search-form input:focus {
  outline: none;
  border-color: rgb(74, 68, 131);
  border-right: none;
}

.search-form button {
  border: none;
  padding: 0.5rem;
  font-size: 1rem;
  background: rgb(74, 68, 131);
  color: white;
  cursor: pointer;
  border-radius: 0 10px 10px 0px;
}

.gallery-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 3px;
  color: rgb(75, 74, 74);
  padding: 0.5rem 0rem;
  border-bottom: 1px solid rgb(159, 156, 180);
}

.download {
  display: flex;
  flex-direction: column;

}

.gallery-info a {
  color: gray;
}

.gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  padding: 2rem 0rem;
  width: 80%;
  margin: auto;
  row-gap: 5rem;
  column-gap: 3rem;
}

.gallery-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.gallery-img-not-found {
  max-width: 100% !important;

  >h4 {
    font-size: 1.5rem;
    font-weight: 100;
  }

}

.nav-button {
  min-height: 30vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.more {
  padding: 1rem 3rem;
  background: rgb(74, 68, 131);
  color: white;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
}

.icon {
  font-size: 1.2rem;
  cursor: pointer;
}

.wrapper {
  position: fixed;
  min-width: 100svw;
  min-height: 100svh;
  display: none;
  /* overflow: hidden; */
  justify-content: center;
  top: 0%;
  left: 0%;
  backdrop-filter: blur(5px);
  z-index: +1;
   background-color:rgba(0, 0, 0, 0.3);
  >.wrapper-img {

    object-position: 50% 50%;

    >img {
      width: auto;
      height: 100svh;
    }
  }


  >button {
    position: absolute;
    top: 5%;
    right: 10%;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 50%;
    color: rgb(255, 255, 255);
    text-shadow: 2px 1px 1px #fff;
    background-color: #afabab59;
    font-weight: 100;
    font-size: 1.5rem;
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    rotate: 0deg;


  }

  >button:hover {
    color: red;
    background-color: #c2baba7f;
    rotate: 15deg;
    scale: 0.9;
    font-size: 1.6rem;
  }
}
