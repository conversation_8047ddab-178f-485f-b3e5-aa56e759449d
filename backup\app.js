const myapiKey = 'moCu4MPWpdRACmO3Z5CazFZsnIIkhRKctVNRV9qYU1600SCYpbBxN9Qp'
const auth = myapiKey //ADD THE AUTH KEY
const gallery = document.querySelector('.gallery')
const searchInput = document.querySelector('.search-input')
const form = document.querySelector('.search-form')
const wraper = document.querySelector('.wrapper')
const wraperImg = document.querySelector('.wrapper-img')

let searchValue
const more = document.querySelector('.more')
let page = 1
let fetchLink
let currentSearch

//Event Listeners
searchInput.addEventListener('input', updateInput)
form.addEventListener('submit', e => {
  e.preventDefault()
  currentSearch = searchValue
  searchPhotos(searchValue)
})
more.addEventListener('click', loadMore)

function updateInput (e) {
  searchValue = e.target.value
}

async function fetchApi (url) {
  const dataFetch = await fetch(url, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: auth
    }
  })

  const data = await dataFetch.json()
  console.log(data)
  return data
}

function generatePictures (data) {
  if (data.photos.length != 0) {
    data.photos.forEach(photo => {
      const galleryImg = document.createElement('div')
      galleryImg.classList.add('gallery-img')
      galleryImg.innerHTML = `
    <img src=${photo.src.large} onclick=fadeOut(this)></img>
            <div class="gallery-info">
            <p title='Photographer ${photo.photographer}'>${photo.photographer}</p>
          
            <div class='download'>
            <small>${photo.width} X ${photo.height}<small/>
               &nbsp;
            <a style={margin:5px 0px 3px 5px} href=${photo.src.original}><i class='fa-solid fa-cloud-arrow-down icon' title='download'></i></a></div>
            </div>
            `
      gallery.appendChild(galleryImg)
    })
  } else {
    // alert(`No results for "${searchValue}"`)
    const galleryImg = document.createElement('div')
    galleryImg.classList.add('gallery-img-not-found')
    galleryImg.innerHTML = `<h4>Sorry No results found for <b>${searchValue}</b></h4>`
    gallery.appendChild(galleryImg)
  }
}

async function curatedPhotos () {
  fetchLink = 'https://api.pexels.com/v1/curated?per_page=12&page=1'
  const data = await fetchApi(fetchLink)
  generatePictures(data)
}

async function searchPhotos (query) {
  clear()
  fetchLink = `https://api.pexels.com/v1/search?query=${query}+query&per_page=15&page=1`
  const data = await fetchApi(fetchLink)
  generatePictures(data)
}

function clear () {
  gallery.innerHTML = ''
  searchInput.value = ''
}

async function loadMore () {
  page++
  if (currentSearch) {
    fetchLink = `https://api.pexels.com/v1/search?query=${currentSearch}+query&per_page=15&page=${page}`
  } else {
    fetchLink = `https://api.pexels.com/v1/curated?per_page=15&page=${page}`
  }
  const data = await fetchApi(fetchLink)
  generatePictures(data)
}

const image = document.querySelector('#test')
const fadeOut = e => {
  if (window.screen.availWidth >= 768) {
    // wraperImg.style.backgroundImage = `url(${e.src})`
    wraper.style.display = 'flex'
    image.src = `${e.src}`
    // e.classList.toggle('scale-up')
  }
}
const closeBtn = document.querySelector('#close-btn')

closeBtn.addEventListener('click', () => {
  hidden()
})
wraper.addEventListener('click', () => {
  hidden()
})
const hidden = () => {
  if (window.screen.availWidth >= 768) {
    wraper.style.display = 'none'
  }
}

curatedPhotos()
