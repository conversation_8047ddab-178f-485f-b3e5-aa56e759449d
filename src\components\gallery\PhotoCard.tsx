import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Download, Info, Heart, User, Eye } from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Skeleton } from '../ui/skeleton';
import type { PhotoCardProps, PexelsPhoto } from '../../types';
import { downloadImage, formatFileSize } from '../../lib/utils';

export function PhotoCard({ photo, onPhotoClick }: PhotoCardProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    downloadImage(photo.src.original, `photo-${photo.id}.jpg`);
  };

  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onPhotoClick(photo);
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    },
    hover: {
      y: -8,
      transition: {
        duration: 0.2,
        ease: "easeOut"
      }
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.2
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.2,
        delay: 0.1
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={() => onPhotoClick(photo)}
      className="group cursor-pointer"
    >
      <Card className="overflow-hidden bg-card backdrop-blur-sm border shadow-md hover:shadow-xl transition-shadow duration-300">
        <div className="relative aspect-[3/4] overflow-hidden bg-muted">
          {/* Loading skeleton */}
          {!isLoaded && (
            <div className="absolute inset-0">
              <Skeleton className="w-full h-full" />
              <div className="absolute bottom-4 left-4 right-4 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          )}

          {/* Main image */}
          <img
            src={photo.src.large}
            alt={photo.alt || `Photo by ${photo.photographer}`}
            className={`w-full h-full object-cover transition-all duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            } group-hover:scale-105`}
            onLoad={() => setIsLoaded(true)}
            loading="lazy"
          />

          {/* Hover overlay */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate={isHovered ? "visible" : "hidden"}
            className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"
          />

          {/* Action buttons */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate={isHovered ? "visible" : "hidden"}
            className="absolute top-3 right-3 flex flex-col gap-2"
          >
            <motion.div variants={buttonVariants}>
              <Button
                size="icon"
                variant="secondary"
                className="h-8 w-8 bg-white/90 hover:bg-white text-black shadow-lg"
                onClick={handleDownload}
                title="Download"
              >
                <Download className="h-4 w-4" />
              </Button>
            </motion.div>
            
            <motion.div variants={buttonVariants}>
              <Button
                size="icon"
                variant="secondary"
                className="h-8 w-8 bg-white/90 hover:bg-white text-black shadow-lg"
                onClick={handleInfoClick}
                title="View Details"
              >
                <Info className="h-4 w-4" />
              </Button>
            </motion.div>
          </motion.div>

          {/* Photo info overlay - Always visible */}
          <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/90 via-black/50 to-transparent">
            <div className="text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 min-w-0 flex-1">
                  <User className="h-3 w-3 flex-shrink-0" />
                  <span className="text-xs font-medium truncate">
                    {photo.photographer}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-xs text-white/80">
                  <span>{photo.width} × {photo.height}</span>
                </div>
              </div>
              {photo.alt && (
                <p className="text-xs text-white/70 mt-1 line-clamp-2">
                  {photo.alt}
                </p>
              )}
            </div>
          </div>

          {/* Color indicator */}
          <div 
            className="absolute top-3 left-3 w-4 h-4 rounded-full border-2 border-white/50 shadow-lg"
            style={{ backgroundColor: photo.avg_color }}
            title={`Average color: ${photo.avg_color}`}
          />
        </div>
      </Card>
    </motion.div>
  );
}
