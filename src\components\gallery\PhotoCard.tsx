import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Download, Info, Heart } from 'lucide-react';
import { Card } from '../ui/card';
import { Button } from '../ui/button';
import { Skeleton } from '../ui/skeleton';
import type { PhotoCardProps } from '../../types';
import { downloadImage } from '../../lib/utils';
import { createPhotographerProfile } from '../../lib/avatarUtils';
import { useLikes } from '../../services/likeService';
import { HoverPreview } from './HoverPreview';

export function PhotoCard({ photo, onPhotoClick }: PhotoCardProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showHoverPreview, setShowHoverPreview] = useState(false);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isLiked, toggleLike } = useLikes();
  const photographerProfile = createPhotographerProfile(
    photo.photographer_id,
    photo.photographer,
    photo.photographer_url
  );

  const handleDownload = (e: React.MouseEvent) => {
    e.stopPropagation();
    downloadImage(photo.src.original, `photo-${photo.id}.jpg`);
  };

  const handleLike = (e: React.MouseEvent) => {
    e.stopPropagation();
    toggleLike(photo.id);
  };

  const handleInfoClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onPhotoClick(photo);
  };

  const handleProfileClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(photo.photographer_url, '_blank');
  };

  // Handle hover preview for large screens only
  const handleMouseEnter = (e: React.MouseEvent) => {
    setIsHovered(true);

    // Only show hover preview on large screens (lg and above)
    if (window.innerWidth >= 1024) {
      const rect = cardRef.current?.getBoundingClientRect();
      if (rect) {
        // Calculate position - show on left side if there's space, otherwise right
        const previewWidth = 320; // Width of the preview card
        const spaceOnLeft = rect.left;
        const spaceOnRight = window.innerWidth - rect.right;

        let x, y;

        if (spaceOnLeft >= previewWidth + 20) {
          // Show on left side
          x = rect.left - previewWidth - 10;
        } else if (spaceOnRight >= previewWidth + 20) {
          // Show on right side
          x = rect.right + 10;
        } else {
          // Center horizontally if no space on either side
          x = Math.max(10, (window.innerWidth - previewWidth) / 2);
        }

        // Ensure preview doesn't go off screen vertically
        y = Math.min(rect.top, window.innerHeight - 400);

        setHoverPosition({ x, y });

        hoverTimeoutRef.current = setTimeout(() => {
          setShowHoverPreview(true);
        }, 500); // Show preview after 500ms hover
      }
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);

    // Delay hiding the preview to allow moving to the preview itself
    setTimeout(() => {
      setShowHoverPreview(false);
    }, 100);

    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
  };

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0
    },
    hover: {
      y: -8
    }
  };

  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: {
        duration: 0.2
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.2,
        delay: 0.1
      }
    }
  };

  return (
    <>
      <motion.div
        ref={cardRef}
        variants={cardVariants}
        initial="hidden"
        animate="visible"
        whileHover="hover"
        transition={{ duration: 0.3, ease: "easeOut" }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={() => onPhotoClick(photo)}
        className="group cursor-pointer"
      >
      <Card className="card-consistent overflow-hidden bg-card backdrop-blur-sm border shadow-md hover:shadow-xl transition-shadow duration-300">
        <div className="relative aspect-square-strict overflow-hidden bg-muted">
          {/* Loading skeleton */}
          {!isLoaded && (
            <div className="absolute inset-0">
              <Skeleton className="w-full h-full" />
              <div className="absolute bottom-4 left-4 right-4 space-y-2">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          )}

          {/* Main image */}
          <img
            src={photo.src.large}
            alt={photo.alt || `Photo by ${photo.photographer}`}
            className={`w-full h-full object-cover transition-all duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            } group-hover:scale-105`}
            onLoad={() => setIsLoaded(true)}
            loading="lazy"
          />

          {/* Hover overlay */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate={isHovered ? "visible" : "hidden"}
            className="absolute inset-0 bg-black/40 backdrop-blur-[1px]"
          />

          {/* Action buttons */}
          <motion.div
            variants={overlayVariants}
            initial="hidden"
            animate={isHovered ? "visible" : "hidden"}
            className="absolute top-3 right-3 flex flex-col gap-2"
          >
            <motion.div variants={buttonVariants}>
              <Button
                size="icon"
                variant="secondary"
                className={`h-8 w-8 shadow-lg ${
                  isLiked(photo.id)
                    ? 'bg-red-500 hover:bg-red-600 text-white'
                    : 'bg-white/90 hover:bg-white text-black'
                }`}
                onClick={handleLike}
                title={isLiked(photo.id) ? "Unlike" : "Like"}
              >
                <Heart className={`h-4 w-4 ${isLiked(photo.id) ? 'fill-current' : ''}`} />
              </Button>
            </motion.div>

            <motion.div variants={buttonVariants}>
              <Button
                size="icon"
                variant="secondary"
                className="h-8 w-8 bg-white/90 hover:bg-white text-black shadow-lg"
                onClick={handleDownload}
                title="Download"
              >
                <Download className="h-4 w-4" />
              </Button>
            </motion.div>

            <motion.div variants={buttonVariants}>
              <Button
                size="icon"
                variant="secondary"
                className="h-8 w-8 bg-white/90 hover:bg-white text-black shadow-lg"
                onClick={handleInfoClick}
                title="View Details"
              >
                <Info className="h-4 w-4" />
              </Button>
            </motion.div>
          </motion.div>

          {/* Photo info overlay - Always visible */}
          <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/90 via-black/50 to-transparent">
            <div className="text-white">
              <div className="flex items-center justify-between">
                <div
                  className="flex items-center space-x-2 min-w-0 flex-1 cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={handleProfileClick}
                  title={`View ${photo.photographer}'s profile`}
                >
                  <img
                    src={photographerProfile.avatar}
                    alt={photo.photographer}
                    className="h-5 w-5 rounded-full bg-white/20 flex-shrink-0"
                  />
                  <span className="text-xs font-medium truncate">
                    {photo.photographer}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-xs text-white/80">
                  <span>{photo.width} × {photo.height}</span>
                </div>
              </div>
              {photo.alt && (
                <p className="text-xs text-white/70 mt-1 line-clamp-2">
                  {photo.alt}
                </p>
              )}
            </div>
          </div>

          {/* Color indicator */}
          <div 
            className="absolute top-3 left-3 w-4 h-4 rounded-full border-2 border-white/50 shadow-lg"
            style={{ backgroundColor: photo.avg_color }}
            title={`Average color: ${photo.avg_color}`}
          />
        </div>
      </Card>
    </motion.div>

    {/* Hover Preview - Only on large screens */}
    <HoverPreview
      photo={photo}
      isVisible={showHoverPreview}
      position={hoverPosition}
      onPhotoClick={onPhotoClick}
    />
  </>
  );
}
